import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  useColorScheme,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';

// Define filter types with their icons
const filterIcons = [
  { id: 'price', name: 'Price', icon: 'cash-outline' },
  { id: 'rating', name: 'Rating', icon: 'star-outline' },
  { id: 'discount', name: 'Discount', icon: 'pricetag-outline' },
  { id: 'size', name: 'Size', icon: 'resize-outline' },
  { id: 'color', name: 'Color', icon: 'color-palette-outline' },
  { id: 'shipping', name: 'Free Shipping', icon: 'car-outline' },
];

interface FilterIconsModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectFilter: (filterId: string) => void;
}

export default function FilterIconsModal({
  visible,
  onClose,
  onSelectFilter
}: FilterIconsModalProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: colors.cardBackground }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Select Filter</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView contentContainerStyle={styles.filtersGrid}>
            {filterIcons.map((filter) => (
              <TouchableOpacity
                key={filter.id}
                style={[styles.filterItem, { backgroundColor: colors.background }]}
                onPress={() => {
                  onSelectFilter(filter.id);
                  onClose();
                }}
              >
                <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                  <Ionicons name={filter.icon as any} size={24} color="#ffffff" />
                </View>
                <Text style={[styles.filterName, { color: colors.text }]}>
                  {filter.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const { width } = Dimensions.get('window');
const itemWidth = (width - (Layout.spacing.md * 2) - (Layout.spacing.sm * 2)) / 3;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: Layout.borderRadius.lg,
    borderTopRightRadius: Layout.borderRadius.lg,
    paddingHorizontal: Layout.spacing.md,
    paddingBottom: Layout.spacing.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  filtersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingVertical: Layout.spacing.md,
  },
  filterItem: {
    width: itemWidth,
    alignItems: 'center',
    padding: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.md,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.xs,
  },
  filterName: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: Layout.spacing.xs,
  },
});
