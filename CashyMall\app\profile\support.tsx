import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TextInput
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import Button from '../../components/Button';

export default function SupportScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();
  
  // Form state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [subject, setSubject] = useState('general');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    message?: string;
  }>({});
  
  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;
    
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
      isValid = false;
    }
    
    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      isValid = false;
    }
    
    if (!email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }
    
    if (!message.trim()) {
      newErrors.message = 'Message is required';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };
  
  const handleSendMessage = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would send the message to your backend
      console.log('Message sent:', {
        firstName,
        lastName,
        email,
        phone,
        subject,
        message
      });
      
      Alert.alert(
        'Message Sent',
        'Thank you for contacting us. We will get back to you as soon as possible.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
      
      // Reset form
      setFirstName('');
      setLastName('');
      setEmail('');
      setPhone('');
      setSubject('general');
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert(
        'Error',
        'There was an error sending your message. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Help & Support</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={[styles.contactForm, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.formTitle, { color: colors.text }]}>
            Contact Us
          </Text>
          
          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>First Name</Text>
              <View style={[
                styles.inputContainer, 
                { backgroundColor: colors.background, borderColor: errors.firstName ? '#ef4444' : colors.border }
              ]}>
                <Ionicons name="person-outline" size={20} color={colors.tabIconDefault} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  placeholder="Enter your first name"
                  placeholderTextColor={colors.tabIconDefault}
                  value={firstName}
                  onChangeText={setFirstName}
                />
              </View>
              {errors.firstName && (
                <Text style={styles.errorText}>{errors.firstName}</Text>
              )}
            </View>
            
            <View style={styles.formColumn}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Last Name</Text>
              <View style={[
                styles.inputContainer, 
                { backgroundColor: colors.background, borderColor: errors.lastName ? '#ef4444' : colors.border }
              ]}>
                <Ionicons name="person-outline" size={20} color={colors.tabIconDefault} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  placeholder="Enter your last name"
                  placeholderTextColor={colors.tabIconDefault}
                  value={lastName}
                  onChangeText={setLastName}
                />
              </View>
              {errors.lastName && (
                <Text style={styles.errorText}>{errors.lastName}</Text>
              )}
            </View>
          </View>
          
          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Email Address</Text>
              <View style={[
                styles.inputContainer, 
                { backgroundColor: colors.background, borderColor: errors.email ? '#ef4444' : colors.border }
              ]}>
                <Ionicons name="mail-outline" size={20} color={colors.tabIconDefault} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  placeholder="Enter your email"
                  placeholderTextColor={colors.tabIconDefault}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  value={email}
                  onChangeText={setEmail}
                />
              </View>
              {errors.email && (
                <Text style={styles.errorText}>{errors.email}</Text>
              )}
            </View>
            
            <View style={styles.formColumn}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Phone Number (optional)</Text>
              <View style={[
                styles.inputContainer, 
                { backgroundColor: colors.background, borderColor: colors.border }
              ]}>
                <Ionicons name="call-outline" size={20} color={colors.tabIconDefault} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  placeholder="Enter your phone number"
                  placeholderTextColor={colors.tabIconDefault}
                  keyboardType="phone-pad"
                  value={phone}
                  onChangeText={setPhone}
                />
              </View>
            </View>
          </View>
          
          <Text style={[styles.inputLabel, { color: colors.text }]}>Subject</Text>
          <View style={styles.radioGroup}>
            <TouchableOpacity 
              style={styles.radioOption}
              onPress={() => setSubject('general')}
            >
              <View style={[
                styles.radioButton, 
                subject === 'general' && { borderColor: colors.primary }
              ]}>
                {subject === 'general' && (
                  <View style={[styles.radioButtonSelected, { backgroundColor: colors.primary }]} />
                )}
              </View>
              <Text style={[styles.radioLabel, { color: colors.text }]}>
                General Inquiry
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.radioOption}
              onPress={() => setSubject('technical')}
            >
              <View style={[
                styles.radioButton, 
                subject === 'technical' && { borderColor: colors.primary }
              ]}>
                {subject === 'technical' && (
                  <View style={[styles.radioButtonSelected, { backgroundColor: colors.primary }]} />
                )}
              </View>
              <Text style={[styles.radioLabel, { color: colors.text }]}>
                Technical Support
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.radioOption}
              onPress={() => setSubject('sales')}
            >
              <View style={[
                styles.radioButton, 
                subject === 'sales' && { borderColor: colors.primary }
              ]}>
                {subject === 'sales' && (
                  <View style={[styles.radioButtonSelected, { backgroundColor: colors.primary }]} />
                )}
              </View>
              <Text style={[styles.radioLabel, { color: colors.text }]}>
                Sales Questions
              </Text>
            </TouchableOpacity>
          </View>
          
          <Text style={[styles.inputLabel, { color: colors.text }]}>Message</Text>
          <View style={[
            styles.messageContainer, 
            { backgroundColor: colors.background, borderColor: errors.message ? '#ef4444' : colors.border }
          ]}>
            <TextInput
              style={[styles.messageInput, { color: colors.text }]}
              placeholder="Write your message here..."
              placeholderTextColor={colors.tabIconDefault}
              multiline
              numberOfLines={5}
              textAlignVertical="top"
              value={message}
              onChangeText={setMessage}
            />
          </View>
          {errors.message && (
            <Text style={styles.errorText}>{errors.message}</Text>
          )}
          
          <Button
            title="Send Message"
            onPress={handleSendMessage}
            isLoading={isLoading}
            fullWidth
            style={styles.sendButton}
            // icon={<Ionicons name="send" size={18} color="#ffffff" />}
          />
        </View>
        
        <View style={styles.contactInfo}>
          <Text style={[styles.contactInfoTitle, { color: colors.text }]}>
            Other Ways to Contact Us
          </Text>
          
          <View style={[styles.contactMethod, { backgroundColor: colors.cardBackground }]}>
            <Ionicons name="mail" size={24} color={colors.primary} />
            <View style={styles.contactMethodInfo}>
              <Text style={[styles.contactMethodTitle, { color: colors.text }]}>
                Email Us
              </Text>
              <Text style={[styles.contactMethodValue, { color: colors.primary }]}>
                <EMAIL>
              </Text>
            </View>
          </View>
          
          <View style={[styles.contactMethod, { backgroundColor: colors.cardBackground }]}>
            <Ionicons name="call" size={24} color={colors.primary} />
            <View style={styles.contactMethodInfo}>
              <Text style={[styles.contactMethodTitle, { color: colors.text }]}>
                Call Us
              </Text>
              <Text style={[styles.contactMethodValue, { color: colors.primary }]}>
                +1 (800) 123-4567
              </Text>
            </View>
          </View>
          
          <View style={[styles.contactMethod, { backgroundColor: colors.cardBackground }]}>
            <Ionicons name="time" size={24} color={colors.primary} />
            <View style={styles.contactMethodInfo}>
              <Text style={[styles.contactMethodTitle, { color: colors.text }]}>
                Business Hours
              </Text>
              <Text style={[styles.contactMethodValue, { color: colors.text }]}>
                Monday - Friday: 9AM - 5PM EST
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Layout.spacing.md,
    paddingBottom: Layout.spacing.xl,
  },
  contactForm: {
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.lg,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: Layout.spacing.sm,
  },
  formColumn: {
    flex: 1,
    marginRight: Layout.spacing.sm,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    height: 48,
    paddingHorizontal: Layout.spacing.sm,
  },
  inputIcon: {
    marginRight: Layout.spacing.sm,
  },
  input: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
  },
  radioGroup: {
    marginBottom: Layout.spacing.md,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#d1d5db',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.sm,
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  radioLabel: {
    fontSize: 16,
  },
  messageContainer: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.md,
    padding: Layout.spacing.sm,
  },
  messageInput: {
    fontSize: 16,
    minHeight: 100,
  },
  sendButton: {
    marginTop: Layout.spacing.sm,
  },
  contactInfo: {
    marginBottom: Layout.spacing.lg,
  },
  contactInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.sm,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
      web: {
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  contactMethodInfo: {
    marginLeft: Layout.spacing.md,
  },
  contactMethodTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  contactMethodValue: {
    fontSize: 14,
  },
});
