import React, { useContext, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  ScrollView,
  Platform,
  Alert,
  Modal,
  TextInput,
  KeyboardAvoidingView
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import Input from '../../components/Input';
import Button from '../../components/Button';
import { AuthContext } from '@/context/AuthContext';

// Mock address data


export default function AddressesScreen() {

   const { user, isSignedIn, signOut } = useContext(AuthContext)!;

const addresses = [
  {
    id: user?.id || '',
    name:  `${user?.firstname} ${user?.lastname}`|| '',
    street:user?.address || '',
    city: user?.city || '',
    state: user?.state || '',
    zipCode: user?.zipCode || '',
    country: user?.country || '',
    phone: user?.phoneNumber || '',
    isDefault: true
  },
  {
    id: '2',
    name: 'John Doe',
    street: '456 Park Ave',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90001',
    country: 'United States',
    phone: '+****************',
    isDefault: false
  }
];
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState<typeof addresses[0] | null>(null);
  const [name, setName] = useState('');
  const [street, setStreet] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [country, setCountry] = useState('');
  const [phone, setPhone] = useState('');
  const [isDefault, setIsDefault] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleEditAddress = (address: typeof addresses[0]) => {
    setEditingAddress(address);
    setName(address.name);
    setStreet(address.street);
    setCity(address.city);
    setState(address.state);
    setZipCode(address.zipCode);
    setCountry(address.country);
    setPhone(address.phone);
    setIsDefault(address.isDefault);
    setModalVisible(true);
  };
  
  const handleAddAddress = () => {
    setEditingAddress(null);
    setName('');
    setStreet('');
    setCity('');
    setState('');
    setZipCode('');
    setCountry('');
    setPhone('');
    setIsDefault(false);
    setModalVisible(true);
  };
  
  const handleDeleteAddress = (addressId: string) => {
    Alert.alert(
      'Delete Address',
      'Are you sure you want to delete this address?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => console.log(`Deleted address ${addressId}`),
          style: 'destructive',
        },
      ]
    );
  };
  
  const handleSetDefaultAddress = (addressId: string) => {
    console.log(`Set address ${addressId} as default`);
  };
  
  const handleSaveAddress = async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (editingAddress) {
        console.log('Updated address:', {
          id: editingAddress.id,
          name,
          street,
          city,
          state,
          zipCode,
          country,
          phone,
          isDefault
        });
      } else {
        console.log('Added new address:', {
          name,
          street,
          city,
          state,
          zipCode,
          country,
          phone,
          isDefault
        });
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('Error saving address:', error);
      Alert.alert(
        'Error',
        'There was an error saving the address. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Shipping Addresses</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {addresses.map(address => (
          <View 
            key={address.id} 
            style={[
              styles.addressCard, 
              { backgroundColor: colors.cardBackground }
            ]}
          >
            {address.isDefault && (
              <View style={[styles.defaultBadge, { backgroundColor: colors.primary }]}>
                <Text style={styles.defaultBadgeText}>Default</Text>
              </View>
            )}
            
            <View style={styles.addressContent}>
              <Text style={[styles.addressName, { color: colors.text }]}>
                {address.name}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.street}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.city}, {address.state} {address.zipCode}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.country}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.phone}
              </Text>
            </View>
            
            <View style={styles.addressActions}>
              <TouchableOpacity 
                style={styles.addressAction}
                onPress={() => handleEditAddress(address)}
              >
                <Ionicons name="create-outline" size={18} color={colors.primary} />
                <Text style={[styles.addressActionText, { color: colors.primary }]}>
                  Edit
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.addressAction}
                onPress={() => handleDeleteAddress(address.id)}
              >
                <Ionicons name="trash-outline" size={18} color="#ef4444" />
                <Text style={[styles.addressActionText, { color: "#ef4444" }]}>
                  Delete
                </Text>
              </TouchableOpacity>
              
              {!address.isDefault && (
                <TouchableOpacity 
                  style={styles.addressAction}
                  onPress={() => handleSetDefaultAddress(address.id)}
                >
                  <Ionicons name="checkmark-circle-outline" size={18} color={colors.primary} />
                  <Text style={[styles.addressActionText, { color: colors.primary }]}>
                    Set as Default
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        ))}
        
        <TouchableOpacity 
          style={[styles.addAddressButton, { borderColor: colors.primary }]}
          onPress={handleAddAddress}
        >
          <Ionicons name="add" size={24} color={colors.primary} />
          <Text style={[styles.addAddressText, { color: colors.primary }]}>
            Add New Address
          </Text>
        </TouchableOpacity>
      </ScrollView>
      
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        >
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {editingAddress ? 'Edit Address' : 'Add New Address'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScrollView}>
              <Input
                label="Full Name"
                placeholder="Enter full name"
                value={name}
                onChangeText={setName}
              />
              
              <Input
                label="Street Address"
                placeholder="Enter street address"
                value={street}
                onChangeText={setStreet}
              />
              
              <Input
                label="City"
                placeholder="Enter city"
                value={city}
                onChangeText={setCity}
              />
              
              <View style={styles.rowInputs}>
                <View style={styles.halfInput}>
                  <Input
                    label="State/Province"
                    placeholder="Enter state"
                    value={state}
                    onChangeText={setState}
                  />
                </View>
                
                <View style={styles.halfInput}>
                  <Input
                    label="ZIP Code"
                    placeholder="Enter ZIP code"
                    value={zipCode}
                    onChangeText={setZipCode}
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              <Input
                label="Country"
                placeholder="Enter country"
                value={country}
                onChangeText={setCountry}
              />
              
              <Input
                label="Phone Number"
                placeholder="Enter phone number"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
              
              <TouchableOpacity 
                style={styles.defaultCheckbox}
                onPress={() => setIsDefault(!isDefault)}
              >
                <View style={[
                  styles.checkbox, 
                  isDefault ? { backgroundColor: colors.primary, borderColor: colors.primary } : { borderColor: colors.border }
                ]}>
                  {isDefault && <Ionicons name="checkmark" size={16} color="#ffffff" />}
                </View>
                <Text style={[styles.checkboxLabel, { color: colors.text }]}>
                  Set as default shipping address
                </Text>
              </TouchableOpacity>
              
              <Button
                title={editingAddress ? 'Update Address' : 'Save Address'}
                onPress={handleSaveAddress}
                isLoading={isLoading}
                fullWidth
                style={styles.saveButton}
              />
            </ScrollView>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Layout.spacing.md,
    paddingBottom: Layout.spacing.xl,
  },
  addressCard: {
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.md,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  defaultBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: 2,
    borderBottomLeftRadius: Layout.borderRadius.md,
    zIndex: 1,
  },
  defaultBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '500',
  },
  addressContent: {
    padding: Layout.spacing.md,
  },
  addressName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    marginBottom: 2,
  },
  addressActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  addressAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Layout.spacing.sm,
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
  },
  addressActionText: {
    fontSize: 14,
    marginLeft: 4,
  },
  addAddressButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  addAddressText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: Layout.spacing.xs,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  modalScrollView: {
    padding: Layout.spacing.md,
  },
  rowInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    width: '48%',
  },
  defaultCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Layout.spacing.md,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    marginRight: Layout.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
  },
  saveButton: {
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },
});
