/**
 * Product API service for the CashyMall mobile app
 */
import { get, post } from './index';
import { PRODUCT_ENDPOINTS, CATEGORY_ENDPOINTS, GetPagedParams } from './endpoints';
import {
  Product,
  ProductListResponse,
  ProductDetailResponse,
  ProductSearchParams,
  Category,
  CategoryListResponse
} from '../../types/product';

/**
 * Get all products
 * @returns List of products
 */
export async function getProducts() {
  const response = await get<ProductListResponse>(PRODUCT_ENDPOINTS.LIST);
  return response;
}

/**
 * Get product details by ID
 * @param id Product ID
 * @returns Product details
 */
export async function getProductDetails(id: number | string) {
  const response = await get<ProductDetailResponse>(`${PRODUCT_ENDPOINTS.DETAILS}/${id}`);
  return response;
}

/**
 * Search products with filters
 * @param params Search parameters or search query string
 * @returns Filtered products
 */
export async function searchProducts(params: ProductSearchParams | string) {
  // Handle string query parameter
  if (typeof params === 'string') {
    const url = `${PRODUCT_ENDPOINTS.SEARCH}?query=${encodeURIComponent(params)}`;
    const response = await get<ProductListResponse>(url);
    return response;
  }

  // Build query string from params object
  const queryParams = new URLSearchParams();

  if (params.query) queryParams.append('query', params.query);
  if (params.categoryId) queryParams.append('categoryId', params.categoryId.toString());
  if (params.brandId) queryParams.append('brandId', params.brandId.toString());
  if (params.minPrice) queryParams.append('minPrice', params.minPrice.toString());
  if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString());
  if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
  if (params.sortBy) queryParams.append('sortBy', params.sortBy);
  if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());

  const queryString = queryParams.toString();
  const url = queryString ? `${PRODUCT_ENDPOINTS.SEARCH}?${queryString}` : PRODUCT_ENDPOINTS.SEARCH;

  const response = await get<ProductListResponse>(url);
  return response;
}

/**
 * Get featured products (random selection from all products)
 * @param limit Number of products to return
 * @returns Random selection of products to be featured
 */
export async function getFeaturedProducts(limit: number = 4) {
  // Get all products
  const response = await getProducts();

  if (response.data && Array.isArray(response.data)) {
    // Randomly select products
    const shuffled = [...response.data].sort(() => 0.5 - Math.random());
    const featuredProducts = shuffled.slice(0, limit);

    return {
      ...response,
      data: featuredProducts
    };
  } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
    // Handle nested data structure
    const shuffled = [...response.data.data].sort(() => 0.5 - Math.random());
    const featuredProducts = shuffled.slice(0, limit);

    return {
      ...response,
      data: {
        ...response.data,
        data: featuredProducts
      }
    };
  }

  return response;
}

/**
 * Get root categories
 * @returns List of root categories
 */
export async function getRootCategories() {
  const response = await get<CategoryListResponse>(CATEGORY_ENDPOINTS.ROOT);
  return response;
}

/**
 * Get category details by ID
 * @param id Category ID
 * @returns Category details
 */
export async function getCategoryDetails(id: number | string) {
  const response = await get<CategoryListResponse>(`${CATEGORY_ENDPOINTS.DETAILS}/${id}`);
  return response;
}

/**
 * Get subcategories by parent category ID
 * @param parentId Parent category ID
 * @returns List of subcategories
 */
export async function getSubcategories(parentId: number | string) {
  const response = await get<CategoryListResponse>(`${CATEGORY_ENDPOINTS.SUBCATEGORIES}/${parentId}`);
  return response;
}

/**
 * Get product categories (legacy method)
 * @returns List of product categories
 * @deprecated Use getRootCategories instead
 */
export async function getProductCategories() {
  return getRootCategories();
}

/**
 * Get products by category
 * @param categoryId Category ID
 * @param limit Number of products to return
 * @returns Products in the specified category
 */
export async function getProductsByCategory(categoryId: number, limit: number = 10) {
  const params: ProductSearchParams = {
    categoryId,
    limit
  };

  return searchProducts(params);
}

/**
 * Get products by brand
 * @param brandId Brand ID
 * @param limit Number of products to return
 * @returns Products from the specified brand
 */
export async function getProductsByBrand(brandId: number, limit: number = 10) {
  const params: ProductSearchParams = {
    brandId,
    limit
  };

  return searchProducts(params);
}

export async function getProductsByBrandId(brandId: number) {
 const response = await get(`${PRODUCT_ENDPOINTS.BRANDS}/${brandId}`);
  return response;
}


export async function getSalesProducts(params: GetPagedParams = {}) {
  const query = new URLSearchParams({
    page: String(params.page ?? 0),
    size: String(params.size ?? 10),
    sortBy: params.sortBy ?? 'name',
    direction: params.direction ?? 'asc',
  });

  const response = await get(`${PRODUCT_ENDPOINTS.sale}?${query.toString()}`);
  return response;
}


/**
 * Get products on sale (with discount)
 * @param limit Number of products to return
 * @returns Products with discounts
 */
export async function getSaleProducts(limit: number = 10) {
  // This is a simplified implementation - the actual API might have a specific endpoint for sale products
  const response = await get<ProductListResponse>(PRODUCT_ENDPOINTS.LIST);

  if (response.data) {
    // Filter products with discount
    const saleProducts = response.data.data.filter(product => product.discountPrice !== null);

    // Limit the number of products
    const limitedProducts = saleProducts.slice(0, limit);

    return {
      ...response,
      data: limitedProducts
    };
  }

  return response;
}
