import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  useColorScheme,
  Switch,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';

// Function to convert color names to hex codes
const getColorHex = (colorName: string): string => {
  const colorMap: {[key: string]: string} = {
    'black': '#000000',
    'white': '#FFFFFF',
    'red': '#FF0000',
    'green': '#00FF00',
    'blue': '#0000FF',
    'yellow': '#FFFF00',
    'purple': '#800080',
    'pink': '#FFC0CB',
    'orange': '#FFA500',
    'brown': '#A52A2A',
    'gray': '#808080',
    'grey': '#808080',
    'silver': '#C0C0C0',
    'gold': '#FFD700',
    'navy': '#000080',
    'teal': '#008080',
    'olive': '#808000',
    'maroon': '#800000'
  };

  return colorMap[colorName] || '#CCCCCC'; // Default to gray if color not found
};

// Function to determine if a color is light or dark
const isLightColor = (hexColor: string): boolean => {
  // Remove the hash if it exists
  const hex = hexColor.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hex.substring(0, 2), 16) || 0;
  const g = parseInt(hex.substring(2, 4), 16) || 0;
  const b = parseInt(hex.substring(4, 6), 16) || 0;

  // Calculate luminance (perceived brightness)
  // Formula: 0.299*R + 0.587*G + 0.114*B
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return true if the color is light (luminance > 0.5)
  return luminance > 0.5;
};

// Define filter option types
type FilterOption = {
  id: string;
  label: string;
  color?: string;
};

type FilterOptionTypes = {
  range: {
    title: string;
    component: 'range';
    min: number;
    max: number;
    step: number;
    options?: never;
  };
  stars: {
    title: string;
    component: 'stars';
    options: number[];
  };
  checkbox: {
    title: string;
    component: 'checkbox';
    options: FilterOption[];
  };
  buttons: {
    title: string;
    component: 'buttons';
    options: FilterOption[];
  };
  color: {
    title: string;
    component: 'color';
    options: FilterOption[];
  };
  toggle: {
    title: string;
    component: 'toggle';
    options: FilterOption[];
  };
  search: {
    title: string;
    component: 'search';
    options?: never;
  };
};

type FilterOptionsType = {
  price: FilterOptionTypes['range'];
  rating: FilterOptionTypes['stars'];
  discount: FilterOptionTypes['checkbox'];
  size: FilterOptionTypes['buttons'];
  color: FilterOptionTypes['color'];
  shipping: FilterOptionTypes['toggle'];
};

// Define filter options for each filter type
const filterOptions: FilterOptionsType = {
  price: {
    title: 'Price Range',
    component: 'range',
    min: 0,
    max: 1000,
    step: 10
  },
  rating: {
    title: 'Rating',
    component: 'stars',
    options: [1, 2, 3, 4, 5]
  },
  discount: {
    title: 'Discount',
    component: 'checkbox',
    options: [
      { id: '10', label: '10% or more' },
      { id: '20', label: '20% or more' },
      { id: '30', label: '30% or more' },
      { id: '50', label: '50% or more' },
      { id: '70', label: '70% or more' }
    ]
  },
  size: {
    title: 'Size',
    component: 'buttons',
    options: [
      { id: 'xs', label: 'XS' },
      { id: 's', label: 'S' },
      { id: 'm', label: 'M' },
      { id: 'l', label: 'L' },
      { id: 'xl', label: 'XL' },
      { id: 'xxl', label: 'XXL' }
    ]
  },
  color: {
    title: 'Color',
    component: 'color',
    options: [
      { id: 'black', label: 'Black', color: '#000000' },
      { id: 'white', label: 'White', color: '#FFFFFF' },
      { id: 'red', label: 'Red', color: '#FF0000' },
      { id: 'blue', label: 'Blue', color: '#0000FF' },
      { id: 'green', label: 'Green', color: '#00FF00' },
      { id: 'yellow', label: 'Yellow', color: '#FFFF00' },
      { id: 'purple', label: 'Purple', color: '#800080' },
      { id: 'pink', label: 'Pink', color: '#FFC0CB' }
    ]
  },
  shipping: {
    title: 'Shipping Options',
    component: 'toggle',
    options: [
      { id: 'free', label: 'Free Shipping' }
    ]
  }
};

interface SpecificFilterModalProps {
  visible: boolean;
  onClose: () => void;
  filterId: string | null;
  onApplyFilter: (filterId: string, values: any) => void;
  availableSizes?: string[];
  availableColors?: string[];
  maxPrice?: number;
}

export default function SpecificFilterModal({
  visible,
  onClose,
  filterId,
  onApplyFilter,
  availableSizes = [],
  availableColors = [],
  maxPrice = 1000
}: SpecificFilterModalProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // State for different filter types
  const [priceRange, setPriceRange] = useState<[number, number]>([0, maxPrice]);
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [toggleOptions, setToggleOptions] = useState<{[key: string]: boolean}>({});

  // Get the current filter configuration
  let currentFilter = filterId && Object.prototype.hasOwnProperty.call(filterOptions, filterId)
    ? {...filterOptions[filterId as keyof typeof filterOptions]}
    : null;

  // Override size options with available sizes from products
  if (currentFilter && filterId === 'size' && availableSizes.length > 0) {
    currentFilter.options = availableSizes.map(size => ({
      id: size,
      label: size
    }));
  } else if (currentFilter && filterId === 'size' && availableSizes.length === 0) {
    // Provide default sizes if none are available
    currentFilter.options = [
      { id: 'S', label: 'S' },
      { id: 'M', label: 'M' },
      { id: 'L', label: 'L' },
      { id: 'XL', label: 'XL' }
    ];
  }

  // Override color options with available colors from products
  if (currentFilter && filterId === 'color' && availableColors.length > 0) {
    currentFilter.options = availableColors.map(color => ({
      id: color.toLowerCase(),
      label: color,
      color: getColorHex(color.toLowerCase())
    }));
  } else if (currentFilter && filterId === 'color' && availableColors.length === 0) {
    // Provide default colors if none are available
    currentFilter.options = [
      { id: 'black', label: 'Black', color: '#000000' },
      { id: 'white', label: 'White', color: '#FFFFFF' },
      { id: 'red', label: 'Red', color: '#FF0000' },
      { id: 'blue', label: 'Blue', color: '#0000FF' }
    ];
  }

  if (!currentFilter || !filterId) {
    // Return an empty modal if no filter is selected
    return (
      <Modal
        visible={visible}
        transparent={true}
        animationType="slide"
        onRequestClose={onClose}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>Filter</Text>
              <TouchableOpacity onPress={onClose}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            <View style={styles.filterContent}>
              <Text style={[styles.notImplemented, { color: colors.text }]}>
                No filter selected
              </Text>
            </View>
          </View>
        </View>
      </Modal>
    );
  }

  const handleApply = () => {
    let values;

    if (!currentFilter) {
      return;
    }

    switch (currentFilter.component) {
      case 'range':
        values = priceRange;
        break;
      case 'stars':
        values = selectedRating;
        break;
      case 'checkbox':
      case 'buttons':
        values = selectedOptions;
        break;
      case 'toggle':
        values = toggleOptions;
        break;
      case 'color':
        values = selectedOptions;
        break;
      default:
        values = null;
    }

    // Log the applied filter values
    console.log(`Applying ${filterId} filter with values:`, values);

    if (filterId) {
      onApplyFilter(filterId, values);
    }
    onClose();
  };

  
  const renderFilterContent = () => {
    switch (currentFilter.component) {
      case 'range':
        return (
          <View style={styles.rangeContainer}>
            <Text style={[styles.rangeValue, { color: colors.text }]}>
              ${priceRange[0]} - ${priceRange[1]}
            </Text>

            {/* Price buttons for quick selection */}
            <Text style={[styles.rangeLabel, { color: colors.text }]}>Price Range</Text>
            <View style={styles.priceButtonsContainer}>
              {(() => {
                // Create 6 evenly distributed price ranges
                const step = Math.ceil(maxPrice / 6);
                const ranges = [];

                for (let i = 0; i < 5; i++) {
                  const min = i * step;
                  const max = (i + 1) * step;
                  ranges.push({
                    label: `$${min}-$${max}`,
                    min,
                    max
                  });
                }

                // Add the last range that goes to the max price
                ranges.push({
                  label: `$${5 * step}+`,
                  min: 5 * step,
                  max: maxPrice
                });

                return ranges;
              })().map((price, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.priceButton,
                    priceRange[0] === price.min && priceRange[1] === price.max &&
                    { backgroundColor: colors.primary }
                  ]}
                  onPress={() => setPriceRange([price.min, price.max])}
                >
                  <Text
                    style={[
                      styles.priceButtonText,
                      priceRange[0] === price.min && priceRange[1] === price.max &&
                      { color: '#ffffff' }
                    ]}
                  >
                    {price.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Custom price range input */}
            <View style={styles.customPriceContainer}>
              <View style={styles.priceInputContainer}>
                <Text style={[styles.priceInputLabel, { color: colors.text }]}>Min $</Text>
                <TextInput
                  style={[styles.priceInput, { borderColor: colors.border, color: colors.text }]}
                  value={priceRange[0].toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 0;
                    setPriceRange([value, Math.max(value, priceRange[1])]);
                  }}
                  placeholder="0"
                  keyboardType="numeric"
                />
              </View>
              <Text style={[styles.priceSeparator, { color: colors.text }]}>-</Text>
              <View style={styles.priceInputContainer}>
                <Text style={[styles.priceInputLabel, { color: colors.text }]}>Max $</Text>
                <TextInput
                  style={[styles.priceInput, { borderColor: colors.border, color: colors.text }]}
                  value={priceRange[1].toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 0;
                    setPriceRange([Math.min(priceRange[0], value), value]);
                  }}
                  placeholder={maxPrice.toString()}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>
        );

      case 'stars':
        return (
          <View style={styles.starsContainer}>
            {[1, 2, 3, 4, 5].map((rating) => (
              <TouchableOpacity
                key={rating}
                style={styles.starButton}
                onPress={() => setSelectedRating(rating)}
              >
                <Ionicons
                  name={rating <= selectedRating ? "star" : "star-outline"}
                  size={30}
                  color={rating <= selectedRating ? colors.warning : colors.tabIconDefault}
                />
                <Text style={[styles.starText, { color: colors.text }]}>
                  {rating}+
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'checkbox':
        return (
          <View style={styles.checkboxContainer}>
            {currentFilter.options.map((option: any) => (
              <TouchableOpacity
                key={option.id}
                style={styles.checkboxItem}
                onPress={() => {
                  if (selectedOptions.includes(option.id)) {
                    setSelectedOptions(selectedOptions.filter(id => id !== option.id));
                  } else {
                    setSelectedOptions([...selectedOptions, option.id]);
                  }
                }}
              >
                <View style={[
                  styles.checkbox,
                  { borderColor: colors.primary },
                  selectedOptions.includes(option.id) && { backgroundColor: colors.primary }
                ]}>
                  {selectedOptions.includes(option.id) && (
                    <Ionicons name="checkmark" size={16} color="#ffffff" />
                  )}
                </View>
                <Text style={[styles.checkboxLabel, { color: colors.text }]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'buttons':
        // For size filter
        return (
          <View style={styles.sizeContainer}>
            {currentFilter.options.map((option: any) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.sizeButton,
                  { borderColor: colors.border },
                  selectedOptions.includes(option.id) && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary
                  }
                ]}
                onPress={() => {
                  if (selectedOptions.includes(option.id)) {
                    setSelectedOptions(selectedOptions.filter(id => id !== option.id));
                  } else {
                    setSelectedOptions([...selectedOptions, option.id]);
                  }
                }}
              >
                <Text
                  style={[
                    styles.sizeButtonText,
                    { color: colors.text },
                    selectedOptions.includes(option.id) && { color: '#ffffff' }
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'color':
        // For color filter
        return (
          <View style={styles.colorContainer}>
            {currentFilter.options.map((option: any) => (
              <TouchableOpacity
                key={option.id}
                style={styles.colorItem}
                onPress={() => {
                  if (selectedOptions.includes(option.id)) {
                    setSelectedOptions(selectedOptions.filter(id => id !== option.id));
                  } else {
                    setSelectedOptions([...selectedOptions, option.id]);
                  }
                }}
              >
                <View
                  style={[
                    styles.colorCircle,
                    { backgroundColor: option.color || '#CCCCCC' },
                    selectedOptions.includes(option.id) && styles.selectedColorCircle
                  ]}
                >
                  {selectedOptions.includes(option.id) && (
                    <Ionicons
                      name="checkmark"
                      size={16}
                      color={isLightColor(option.color) ? '#000000' : '#ffffff'}
                    />
                  )}
                </View>
                <Text style={[styles.colorLabel, { color: colors.text }]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'toggle':
        // For shipping filter
        return (
          <View style={styles.toggleContainer}>
            {currentFilter.options.map((option: any) => (
              <View key={option.id} style={styles.toggleItem}>
                <Text style={[styles.toggleLabel, { color: colors.text }]}>
                  {option.label}
                </Text>
                <Switch
                  value={toggleOptions[option.id] || false}
                  onValueChange={(value) => {
                    setToggleOptions({
                      ...toggleOptions,
                      [option.id]: value
                    });
                  }}
                  trackColor={{ false: colors.border, true: colors.primary }}
                  thumbColor="#ffffff"
                />
              </View>
            ))}
          </View>
        );

      default:
        return (
          <Text style={[styles.notImplemented, { color: colors.text }]}>
            This filter type is not yet implemented
          </Text>
        );
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: colors.cardBackground }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>{currentFilter.title}</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView contentContainerStyle={styles.filterContent}>
            {renderFilterContent()}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.resetButton, { borderColor: colors.border }]}
              onPress={() => {
                // Reset the current filter
                setPriceRange([0, maxPrice]);
                setSelectedRating(0);
                setSelectedOptions([]);
                setToggleOptions({});
              }}
            >
              <Text style={[styles.buttonText, { color: colors.text }]}>Reset</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.applyButton, { backgroundColor: colors.primary }]}
              onPress={handleApply}
            >
              <Text style={[styles.buttonText, { color: '#ffffff' }]}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: Layout.borderRadius.lg,
    borderTopRightRadius: Layout.borderRadius.lg,
    paddingHorizontal: Layout.spacing.md,
    paddingBottom: Layout.spacing.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  filterContent: {
    paddingVertical: Layout.spacing.md,
    minHeight: 100,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Layout.spacing.md,
    paddingTop: Layout.spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  button: {
    flex: 1,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  resetButton: {
    marginRight: Layout.spacing.sm,
    borderWidth: 1,
  },
  applyButton: {
    marginLeft: Layout.spacing.sm,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  rangeContainer: {
    padding: Layout.spacing.md,
  },
  rangeValue: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: Layout.spacing.md,
  },
  rangeLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: Layout.spacing.sm,
    marginBottom: Layout.spacing.xs,
  },
  priceButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: Layout.spacing.md,
  },
  priceButton: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    marginBottom: Layout.spacing.sm,
    width: '48%',
    alignItems: 'center',
  },
  priceButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  customPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: Layout.spacing.sm,
  },
  priceInputContainer: {
    width: '45%',
  },
  priceInputLabel: {
    fontSize: 12,
    marginBottom: Layout.spacing.xs,
  },
  priceInput: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    fontSize: 14,
  },
  priceSeparator: {
    fontSize: 16,
    fontWeight: '600',
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
  },
  starButton: {
    alignItems: 'center',
  },
  starText: {
    marginTop: Layout.spacing.xs,
    fontSize: 12,
  },
  checkboxContainer: {
    padding: Layout.spacing.md,
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderRadius: 4,
    marginRight: Layout.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxLabel: {
    fontSize: 16,
  },
  sizeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: Layout.spacing.md,
  },
  sizeButton: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    margin: Layout.spacing.xs,
  },
  sizeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  colorContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: Layout.spacing.md,
  },
  colorItem: {
    alignItems: 'center',
    margin: Layout.spacing.sm,
    width: 60,
  },
  colorCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.xs,
  },
  selectedColorCircle: {
    borderWidth: 2,
    borderColor: '#000000',
  },
  colorLabel: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: Layout.spacing.xs,
  },
  toggleContainer: {
    padding: Layout.spacing.md,
  },
  toggleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  notImplemented: {
    padding: Layout.spacing.md,
    fontSize: 16,
    textAlign: 'center',
  },
});
