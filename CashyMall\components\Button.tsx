import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps
} from 'react-native';
import { useColorScheme } from 'react-native';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function Button({
  title,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  fullWidth = false,
  style,
  textStyle,
  ...props
}: ButtonProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  const getButtonStyle = () => {
    let buttonStyle: ViewStyle = {};
    
    // Variant styles
    switch (variant) {
      case 'primary':
        buttonStyle.backgroundColor = colors.primary;
        break;
      case 'secondary':
        buttonStyle.backgroundColor = colors.secondary;
        break;
      case 'outline':
        buttonStyle.backgroundColor = 'transparent';
        buttonStyle.borderWidth = 1;
        buttonStyle.borderColor = colors.primary;
        break;
      case 'ghost':
        buttonStyle.backgroundColor = 'transparent';
        break;
    }
    
    // Size styles
    switch (size) {
      case 'sm':
        buttonStyle.paddingVertical = Layout.spacing.xs;
        buttonStyle.paddingHorizontal = Layout.spacing.md;
        break;
      case 'md':
        buttonStyle.paddingVertical = Layout.spacing.sm;
        buttonStyle.paddingHorizontal = Layout.spacing.lg;
        break;
      case 'lg':
        buttonStyle.paddingVertical = Layout.spacing.md;
        buttonStyle.paddingHorizontal = Layout.spacing.xl;
        break;
    }
    
    // Width style
    if (fullWidth) {
      buttonStyle.width = '100%';
    }
    
    return buttonStyle;
  };

  const getTextStyle = () => {
    let textStyle: TextStyle = {};
    
    // Variant text styles
    switch (variant) {
      case 'primary':
      case 'secondary':
        textStyle.color = '#ffffff';
        break;
      case 'outline':
      case 'ghost':
        textStyle.color = colors.primary;
        break;
    }
    
    // Size text styles
    switch (size) {
      case 'sm':
        textStyle.fontSize = 14;
        break;
      case 'md':
        textStyle.fontSize = 16;
        break;
      case 'lg':
        textStyle.fontSize = 18;
        break;
    }
    
    return textStyle;
  };

  return (
    <TouchableOpacity
      style={[styles.button, getButtonStyle(), style]}
      disabled={isLoading}
      {...props}
    >
      {isLoading ? (
        <ActivityIndicator 
          color={variant === 'primary' || variant === 'secondary' ? '#ffffff' : colors.primary} 
          size="small" 
        />
      ) : (
        <Text style={[styles.text, getTextStyle(), textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
  },
});
