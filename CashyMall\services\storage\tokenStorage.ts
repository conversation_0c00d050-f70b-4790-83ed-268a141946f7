/**
 * Token storage service for the CashyMall mobile app
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '../api/auth';
import { post } from '../api';
import { AUTH_ENDPOINTS } from '../api/endpoints';

// Storage keys
const TOKEN_KEY = '@CashyMall:token';
const USER_KEY = '@CashyMall:user';
const TOKEN_EXPIRY_KEY = '@CashyMall:tokenExpiry';

// Default token expiry in seconds (24 hours)
const DEFAULT_TOKEN_EXPIRY = 24 * 60 * 60;

// Auth data interface
interface AuthData {
  token: string;
  user: User;
  expiresIn?: number;
}

/**
 * Save authentication data to storage
 * @param authData Authentication data
 */
export async function saveToken(authData: AuthData): Promise<void> {
  try {
    const { token, user, expiresIn = DEFAULT_TOKEN_EXPIRY } = authData;

    // Calculate expiry time in milliseconds
    const expiryTime = Date.now() + expiresIn * 1000;

    // Store token, user data, and expiry time
    await AsyncStorage.multiSet([
      [TOKEN_KEY, token],
      [USER_KEY, JSON.stringify(user)],
      [TOKEN_EXPIRY_KEY, expiryTime.toString()],
    ]);
  } catch (error) {
    console.error('Error saving auth data:', error);
  }
}

/**
 * Get stored auth data
 * @returns Stored auth data or null if not found
 */
export async function getToken(): Promise<{ token: string; user: User; expiresIn: number } | null> {
  try {
    // Get token, user data, and expiry time from storage
    const values = await AsyncStorage.multiGet([
      TOKEN_KEY,
      USER_KEY,
      TOKEN_EXPIRY_KEY,
    ]);

    const token = values[0][1];
    const userJson = values[1][1];
    const expiryTime = values[2][1];

    if (!token || !userJson || !expiryTime) {
      return null;
    }

    // Parse user data
    const user = JSON.parse(userJson);

    // Calculate remaining time in seconds
    const expiresIn = Math.floor((parseInt(expiryTime) - Date.now()) / 1000);

    return {
      token,
      user,
      expiresIn,
    };
  } catch (error) {
    console.error('Error getting auth data:', error);
    return null;
  }
}

/**
 * Get stored user data
 * @returns Stored user data or null if not found
 */
export async function getUser(): Promise<User | null> {
  try {
    const userJson = await AsyncStorage.getItem(USER_KEY);

    if (!userJson) {
      return null;
    }

    return JSON.parse(userJson);
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
}

/**
 * Check if access token is expired
 * @returns True if token is expired, false otherwise
 */
export async function isTokenExpired(): Promise<boolean> {
  try {
    const expiryTime = await AsyncStorage.getItem(TOKEN_EXPIRY_KEY);

    if (!expiryTime) {
      return true;
    }

    // Check if current time is past expiry time
    return Date.now() > parseInt(expiryTime);
  } catch (error) {
    console.error('Error checking token expiry:', error);
    return true;
  }
}

/**
 * Refresh access token
 * @returns True if token was refreshed successfully, false otherwise
 */
export async function refreshAccessToken(): Promise<boolean> {
  try {
    // Get current token
    const authData = await getToken();

    if (!authData || !authData.token) {
      return false;
    }

    // Request new token
    const response = await post(
      AUTH_ENDPOINTS.REFRESH_TOKEN,
      { token: authData.token },
      { requiresAuth: false, skipRefreshToken: true }
    );

    if (response.data && response.data.token) {
      // Save new token
      await saveToken({
        token: response.data.token,
        user: response.data.user || authData.user
      });
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  }
}

/**
 * Clear all stored auth data
 */
export async function clearTokens(): Promise<void> {
  try {
    await AsyncStorage.multiRemove([
      TOKEN_KEY,
      USER_KEY,
      TOKEN_EXPIRY_KEY,
    ]);
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
}

/**
 * Check if user is authenticated
 * @returns True if user is authenticated, false otherwise
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const token = await getToken();

    if (!token) {
      return false;
    }

    // Check if token is expired
    if (await isTokenExpired()) {
      // Try to refresh token
      return await refreshAccessToken();
    }

    return true;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
}
