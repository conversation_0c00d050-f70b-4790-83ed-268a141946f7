/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/brands` | `/brands`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/sale` | `/sale`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/shop` | `/shop`; params?: Router.UnknownInputParams; } | { pathname: `/auth/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/reset-password`; params?: Router.UnknownInputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownInputParams; } | { pathname: `/cart/checkout`; params?: Router.UnknownInputParams; } | { pathname: `/cart`; params?: Router.UnknownInputParams; } | { pathname: `/profile/addresses`; params?: Router.UnknownInputParams; } | { pathname: `/profile/change-password`; params?: Router.UnknownInputParams; } | { pathname: `/profile/edit`; params?: Router.UnknownInputParams; } | { pathname: `/profile/order-details`; params?: Router.UnknownInputParams; } | { pathname: `/profile/orders`; params?: Router.UnknownInputParams; } | { pathname: `/profile/settings`; params?: Router.UnknownInputParams; } | { pathname: `/profile/support`; params?: Router.UnknownInputParams; } | { pathname: `/profile/wishlist`; params?: Router.UnknownInputParams; } | { pathname: `/product/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/search`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/brands` | `/brands`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/sale` | `/sale`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/shop` | `/shop`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/reset-password`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownOutputParams; } | { pathname: `/cart/checkout`; params?: Router.UnknownOutputParams; } | { pathname: `/cart`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/addresses`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/change-password`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/edit`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/order-details`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/orders`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/support`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/wishlist`; params?: Router.UnknownOutputParams; } | { pathname: `/product/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/brands${`?${string}` | `#${string}` | ''}` | `/brands${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/sale${`?${string}` | `#${string}` | ''}` | `/sale${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/shop${`?${string}` | `#${string}` | ''}` | `/shop${`?${string}` | `#${string}` | ''}` | `/auth/forgot-password${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/reset-password${`?${string}` | `#${string}` | ''}` | `/auth/signup${`?${string}` | `#${string}` | ''}` | `/cart/checkout${`?${string}` | `#${string}` | ''}` | `/cart${`?${string}` | `#${string}` | ''}` | `/profile/addresses${`?${string}` | `#${string}` | ''}` | `/profile/change-password${`?${string}` | `#${string}` | ''}` | `/profile/edit${`?${string}` | `#${string}` | ''}` | `/profile/order-details${`?${string}` | `#${string}` | ''}` | `/profile/orders${`?${string}` | `#${string}` | ''}` | `/profile/settings${`?${string}` | `#${string}` | ''}` | `/profile/support${`?${string}` | `#${string}` | ''}` | `/profile/wishlist${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/brands` | `/brands`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/sale` | `/sale`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/shop` | `/shop`; params?: Router.UnknownInputParams; } | { pathname: `/auth/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/reset-password`; params?: Router.UnknownInputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownInputParams; } | { pathname: `/cart/checkout`; params?: Router.UnknownInputParams; } | { pathname: `/cart`; params?: Router.UnknownInputParams; } | { pathname: `/profile/addresses`; params?: Router.UnknownInputParams; } | { pathname: `/profile/change-password`; params?: Router.UnknownInputParams; } | { pathname: `/profile/edit`; params?: Router.UnknownInputParams; } | { pathname: `/profile/order-details`; params?: Router.UnknownInputParams; } | { pathname: `/profile/orders`; params?: Router.UnknownInputParams; } | { pathname: `/profile/settings`; params?: Router.UnknownInputParams; } | { pathname: `/profile/support`; params?: Router.UnknownInputParams; } | { pathname: `/profile/wishlist`; params?: Router.UnknownInputParams; } | `/product/${Router.SingleRoutePart<T>}` | { pathname: `/product/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
