/**
 * Storage utilities for the CashyMall mobile app
 */
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Store data in AsyncStorage
 * @param key Storage key
 * @param value Data to store
 */
export async function storeData(key: string, value: any): Promise<void> {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error(`Error storing data for key ${key}:`, error);
  }
}

/**
 * Retrieve data from AsyncStorage
 * @param key Storage key
 * @returns Stored data or null if not found
 */
export async function getData<T = any>(key: string): Promise<T | null> {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (error) {
    console.error(`Error retrieving data for key ${key}:`, error);
    return null;
  }
}

/**
 * Remove data from AsyncStorage
 * @param key Storage key
 */
export async function removeData(key: string): Promise<void> {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing data for key ${key}:`, error);
  }
}

/**
 * Clear all data from AsyncStorage
 */
export async function clearAllData(): Promise<void> {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Error clearing all data:', error);
  }
}

/**
 * Get all keys from AsyncStorage
 * @returns Array of storage keys
 */
export async function getAllKeys(): Promise<string[]> {
  try {
    return await AsyncStorage.getAllKeys();
  } catch (error) {
    console.error('Error getting all keys:', error);
    return [];
  }
}

export default {
  storeData,
  getData,
  removeData,
  clearAllData,
  getAllKeys,
};
