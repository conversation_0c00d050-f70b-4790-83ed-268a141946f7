import { WishlistResponse } from "@/types/product";
import { del, get, post } from ".";
import { WISHLIST_ENDPOINTS } from "./endpoints";



/**
 * Get all wishlists
 * @returns List of wishlists
 */

export async function getWishlists() {
  const response = await get(WISHLIST_ENDPOINTS.GET);
  return response ;
}

/**
 * Add item to wishlist
 * @param productId Product ID
 */
export async function addToWishlist(productId: string) {
  const response = await post(WISHLIST_ENDPOINTS.ADD, {
    productId
  });
  return response;
}
export async function checkWishlist(productId: string) {
  const response = await get(`${WISHLIST_ENDPOINTS.check}/${productId}`);
  return response;
}

/**
 * Remove item from wishlist
 * @param productId Product ID
 */
export async function removeFromWishlist(productId: string) {
  const response = await del(`${WISHLIST_ENDPOINTS.REMOVE}/${productId}`);
  return response;
}


export async function clearWishlist() {
  const response = await del(WISHLIST_ENDPOINTS.clear);
  return response;
}
