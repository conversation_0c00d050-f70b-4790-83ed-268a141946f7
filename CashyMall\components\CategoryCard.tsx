import { Link } from 'expo-router';
import React from 'react';
import {
    ImageBackground,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';

interface CategoryCardProps {
  id: string;
  name: string;
  imageUrl: string;
  productCount: number;
}

export default function CategoryCard({
  id,
  name,
  imageUrl,
  productCount,
}: CategoryCardProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Create style objects with dynamic properties to avoid array styles for web
  const buttonStyle = {
    ...styles.button,
    backgroundColor: colors.primary
  };

  return (
    <Link href={`/shop?category=${id}`} asChild>
      <TouchableOpacity style={styles.container}>
        <ImageBackground
          source={{ uri: imageUrl }}
          style={styles.background}
          imageStyle={styles.backgroundImage}
        >
          <View style={styles.overlay}>
            <Text style={styles.name}>{name}</Text>
            <Text style={styles.count}>{productCount} Products</Text>
            <View style={buttonStyle}>
              <Text style={styles.buttonText}>Shop Now</Text>
            </View>
          </View>
        </ImageBackground>
      </TouchableOpacity>
    </Link>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 180,
    
    borderRadius: Layout.borderRadius.md,
    overflow: 'hidden',
    marginBottom: Layout.spacing.md,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  background: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },
  backgroundImage: {
    borderRadius: Layout.borderRadius.md,
  },
  overlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    padding: Layout.spacing.md,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  name: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: Layout.spacing.xs,
  },
  count: {
    color: '#ffffff',
    fontSize: 14,
    marginBottom: Layout.spacing.md,
  },
  button: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});
