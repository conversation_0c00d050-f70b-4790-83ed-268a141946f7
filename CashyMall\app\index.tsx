import React from "react";
import {
  Text,
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  useColorScheme,
  ScrollView,
  Dimensions,
  Platform
} from "react-native";
import { Link, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Colors from "../constants/Colors";
import Layout from "../constants/Layout";

export default function Index() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Create style objects with dynamic properties to avoid array styles for web
  const containerStyle = {
    ...styles.container,
    backgroundColor: colors.background
  };

  const titleStyle = {
    ...styles.title,
    color: colors.text
  };

  const subtitleStyle = {
    ...styles.subtitle,
    color: colors.text
  };

  const featureItemStyle = {
    ...styles.featureItem,
    backgroundColor: colors.cardBackground
  };

  const featureTitleStyle = {
    ...styles.featureTitle,
    color: colors.text
  };

  const featureDescriptionStyle = {
    ...styles.featureDescription,
    color: colors.tabIconDefault
  };

  const buttonStyle = {
    ...styles.button,
    backgroundColor: colors.primary
  };

  const signInTextStyle = {
    ...styles.signInText,
    color: colors.text
  };

  const signInLinkStyle = {
    ...styles.signInLink,
    color: colors.primary
  };

  return (
    <View style={containerStyle}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/images/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* Welcome Text */}
        <Text style={titleStyle}>
          Welcome to CashyMall
        </Text>
        <Text style={subtitleStyle}>
          Your one-stop shop for the best deals and products
        </Text>

        {/* Features */}
        <View style={styles.featuresContainer}>
          <View style={featureItemStyle}>
            <Ionicons name="pricetag-outline" size={32} color={colors.primary} />
            <Text style={featureTitleStyle}>Great Deals</Text>
            <Text style={featureDescriptionStyle}>
              Discover amazing discounts on your favorite products
            </Text>
          </View>

          <View style={featureItemStyle}>
            <Ionicons name="cube-outline" size={32} color={colors.primary} />
            <Text style={featureTitleStyle}>Quality Products</Text>
            <Text style={featureDescriptionStyle}>
              Shop from a wide range of high-quality items
            </Text>
          </View>

          <View style={featureItemStyle}>
            <Ionicons name="rocket-outline" size={32} color={colors.primary} />
            <Text style={featureTitleStyle}>Fast Delivery</Text>
            <Text style={featureDescriptionStyle}>
              Get your orders delivered quickly to your doorstep
            </Text>
          </View>
        </View>

        {/* Get Started Button */}
        <TouchableOpacity
          style={buttonStyle}
          onPress={() => router.replace('/(tabs)')}
        >
          <Text style={styles.buttonText}>Get Started</Text>
          <Ionicons name="arrow-forward" size={20} color="#ffffff" />
        </TouchableOpacity>

        {/* Sign In Link */}
        <View style={styles.signInContainer}>
          <Text style={signInTextStyle}>
            Already have an account?
          </Text>
          <TouchableOpacity onPress={() => router.push('/auth/login')}>
            <Text style={signInLinkStyle}>
              Sign In
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xxl * 1.5,
    paddingBottom: Layout.spacing.xl,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: Layout.spacing.xl,
  },
  logo: {
    width: 150,
    height: 80,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: Layout.spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Layout.spacing.xl,
    paddingHorizontal: Layout.spacing.md,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: Layout.spacing.xl,
  },
  featureItem: {
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.md,
    alignItems: 'center',
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: Layout.spacing.sm,
    marginBottom: Layout.spacing.xs,
  },
  featureDescription: {
    fontSize: 14,
    textAlign: 'center',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Layout.spacing.md,
    paddingHorizontal: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.md,
    width: '100%',
    marginBottom: Layout.spacing.lg,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginRight: Layout.spacing.sm,
  },
  signInContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  signInText: {
    fontSize: 14,
    marginRight: Layout.spacing.xs,
  },
  signInLink: {
    fontSize: 14,
    fontWeight: '600',
  },
});
