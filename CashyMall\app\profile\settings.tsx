import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  ScrollView,
  Switch,
  Alert,
  Platform
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { AuthContext } from '../../context/AuthContext';

export default function SettingsScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();
  const { signOut } = useContext(AuthContext)!;

  // Settings state
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [orderUpdates, setOrderUpdates] = useState(true);
  const [promotions, setPromotions] = useState(true);
  const [darkMode, setDarkMode] = useState(colorScheme === 'dark');

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Error', 'Failed to logout. Please try again.');
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            // In a real app, you would call an API to delete the account
            console.log('Account deleted');
            router.replace('/(tabs)');
          },
          style: 'destructive',
        },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Account Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Account Settings
          </Text>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => router.push('/profile/edit')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="person-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Edit Profile
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => router.push('/profile/change-password')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="lock-closed-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Change Password
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => router.push('/profile/addresses')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="location-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Manage Addresses
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
          </TouchableOpacity>
        </View>

        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Notification Settings
          </Text>

          <View style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.settingContent}>
              <Ionicons name="notifications-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Push Notifications
              </Text>
            </View>
            <Switch
              value={pushNotifications}
              onValueChange={setPushNotifications}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor={Platform.OS === 'ios' ? '#ffffff' : pushNotifications ? '#ffffff' : '#f4f3f4'}
              ios_backgroundColor="#767577"
            />
          </View>

          <View style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.settingContent}>
              <Ionicons name="mail-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Email Notifications
              </Text>
            </View>
            <Switch
              value={emailNotifications}
              onValueChange={setEmailNotifications}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor={Platform.OS === 'ios' ? '#ffffff' : emailNotifications ? '#ffffff' : '#f4f3f4'}
              ios_backgroundColor="#767577"
            />
          </View>

          <View style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.settingContent}>
              <Ionicons name="cube-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Order Updates
              </Text>
            </View>
            <Switch
              value={orderUpdates}
              onValueChange={setOrderUpdates}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor={Platform.OS === 'ios' ? '#ffffff' : orderUpdates ? '#ffffff' : '#f4f3f4'}
              ios_backgroundColor="#767577"
            />
          </View>

          <View style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.settingContent}>
              <Ionicons name="pricetag-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Promotions & Offers
              </Text>
            </View>
            <Switch
              value={promotions}
              onValueChange={setPromotions}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor={Platform.OS === 'ios' ? '#ffffff' : promotions ? '#ffffff' : '#f4f3f4'}
              ios_backgroundColor="#767577"
            />
          </View>
        </View>

        {/* App Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            App Settings
          </Text>

          <View style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.settingContent}>
              <Ionicons name="moon-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Dark Mode
              </Text>
            </View>
            <Switch
              value={darkMode}
              onValueChange={setDarkMode}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor={Platform.OS === 'ios' ? '#ffffff' : darkMode ? '#ffffff' : '#f4f3f4'}
              ios_backgroundColor="#767577"
            />
          </View>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => console.log('Language settings')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="language-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Language
              </Text>
            </View>
            <View style={styles.settingValue}>
              <Text style={[styles.settingValueText, { color: colors.tabIconDefault }]}>
                English
              </Text>
              <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Support & About */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Support & About
          </Text>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => router.push('/profile/support')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="help-circle-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Help & Support
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => console.log('Privacy policy')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="shield-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Privacy Policy
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => console.log('Terms of service')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="document-text-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Terms of Service
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.cardBackground }]}
            onPress={() => console.log('About app')}
          >
            <View style={styles.settingContent}>
              <Ionicons name="information-circle-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                About CashyMall
              </Text>
            </View>
            <View style={styles.settingValue}>
              <Text style={[styles.settingValueText, { color: colors.tabIconDefault }]}>
                Version 1.0.0
              </Text>
              <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Account Actions */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.actionButton, { borderColor: colors.border }]}
            onPress={handleLogout}
          >
            <Ionicons name="log-out-outline" size={22} color="#ef4444" />
            <Text style={styles.logoutText}>
              Logout
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.deleteAccountButton}
            onPress={handleDeleteAccount}
          >
            <Text style={styles.deleteAccountText}>
              Delete Account
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: Layout.spacing.lg,
    paddingHorizontal: Layout.spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.sm,
    marginTop: Layout.spacing.md,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.sm,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
      web: {
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    marginLeft: Layout.spacing.sm,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: 14,
    marginRight: Layout.spacing.xs,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    marginBottom: Layout.spacing.md,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#ef4444',
    marginLeft: Layout.spacing.sm,
  },
  deleteAccountButton: {
    alignItems: 'center',
    padding: Layout.spacing.sm,
  },
  deleteAccountText: {
    fontSize: 14,
    color: '#9ca3af',
    textDecorationLine: 'underline',
  },
});
