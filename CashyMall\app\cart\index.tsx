import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
    FlatList,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    useColorScheme,
    View,
    Alert
} from 'react-native';
import Button from '../../components/Button';
import CartItem from '../../components/CartItem';
import Header from '../../components/Header';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getCart, updateCartItem, removeFromCart } from '../../services/api/cart';
import type { CartItem as CartItemType } from '../../types/product';

export default function CartScreen() {
  const [cartItems, setCartItems] = useState<CartItemType[]>([]);
  const [promoCode, setPromoCode] = useState('');
  const [promoApplied, setPromoApplied] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalprice, setTotalPrice] = useState(0);

  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Fetch cart items
  useEffect(() => {
    fetchCartItems();
  }, []);

  const fetchCartItems = async () => {
    try {
      setLoading(true);
      const response = await getCart();
      if (response.data && Array.isArray(response.data.items)) {
        setCartItems(response.data.items);
        setTotalPrice(response.data.totalPrice);
        setError(null);
      } else {
        setError('Invalid cart data format received from API');
      }
    } catch (err) {
      setError('Failed to load cart items');
      console.error('Error fetching cart:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = async (id : number) => {
    try {
      const response = await removeFromCart(id);
      if (response.error) {
        Alert.alert('Error', 'Failed to remove item from cart');
        return;
      }
      // After successful removal, fetch updated cart to get new total
      await fetchCartItems();
    } catch (err) {
      console.error('Error removing item:', err);
      Alert.alert('Error', 'Failed to remove item from cart');
    }
  };

  const handleUpdateQuantity = async (id : number, quantity: number) => {
    try {
      const response = await updateCartItem(id, quantity);
      if (response.error) {
        Alert.alert('Error', 'Failed to update cart');
        return;
      }
      // After successful update, fetch updated cart to get new total
      await fetchCartItems();
    } catch (err) {
      console.error('Error updating quantity:', err);
      Alert.alert('Error', 'Failed to update cart');
    }
  };

  const handleApplyPromo = () => {
    // In a real app, you would validate the promo code with your backend
    if (promoCode.toLowerCase() === 'discount20') {
      setPromoApplied(true);
    }
  };

  const handleCheckout = () => {
    router.push('/cart/checkout');
  };

  // Calculate cart totals
  const subtotal = totalprice;
  const shipping = 0;
  const total = promoApplied ? subtotal * 0.8 : subtotal; // Apply 20% discount if promo is applied

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title="Shopping Cart" showBack showCart={false} />
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}>
            Loading cart...
          </Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title="Shopping Cart" showBack showCart={false} />
        <View style={styles.emptyContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            Oops!
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}>
            {error}
          </Text>
          <Button
            title="Try Again"
            style={styles.shopButton}
            onPress={fetchCartItems}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="Shopping Cart" showBack showCart={false} />

      {cartItems.length > 0 ? (
        <>
          <FlatList
            data={cartItems}
            renderItem={({ item }) => (
              <CartItem
                id={item.id}
                name={item.productName}
                price={item.productPrice}
                quantity={item.quantity}
                imageUrl={item.imageUrl}
                onRemove={handleRemoveItem}
                onUpdateQuantity={handleUpdateQuantity}
              />
            )}
            keyExtractor={item => String(item.id)}
            contentContainerStyle={styles.cartItemsContainer}
            showsVerticalScrollIndicator={false}
          />

          <View style={[styles.summaryContainer, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.promoContainer}>
              <View style={[styles.promoInputContainer, { borderColor: colors.border }]}>
                <TextInput
                  style={[styles.promoInput, { color: colors.text }]}
                  placeholder="Enter promo code"
                  placeholderTextColor={colors.tabIconDefault}
                  value={promoCode}
                  onChangeText={setPromoCode}
                  editable={!promoApplied}
                />
              </View>

              <Button
                title={promoApplied ? "Applied" : "Apply"}
                variant={promoApplied ? "secondary" : "primary"}
                size="sm"
                style={styles.promoButton}
                onPress={handleApplyPromo}
                disabled={promoApplied || !promoCode}
              />
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.text }]}>Subtotal</Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>${subtotal.toFixed(2)}</Text>
            </View>

          

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.text }]}>Shipping</Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>${shipping.toFixed(2)}</Text>
            </View>

            <View style={[styles.totalRow, { borderTopColor: colors.border }]}>
              <Text style={[styles.totalLabel, { color: colors.text }]}>Total</Text>
              <Text style={[styles.totalValue, { color: colors.primary }]}>${total.toFixed(2)}</Text>
            </View>

            <Button
              title="Proceed to Checkout"
              fullWidth
              style={styles.checkoutButton}
              onPress={handleCheckout}
            />
          </View>
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="cart-outline" size={64} color={colors.tabIconDefault} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            Your cart is empty
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}>
            Looks like you haven't added any products to your cart yet.
          </Text>
          <Button
            title="Start Shopping"
            style={styles.shopButton}
            onPress={() => router.push('/shop')}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  cartItemsContainer: {
    padding: Layout.spacing.md,
  },
  summaryContainer: {
    padding: Layout.spacing.md,
    borderTopLeftRadius: Layout.borderRadius.lg,
    borderTopRightRadius: Layout.borderRadius.lg,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
      web: {
        boxShadow: '0 -2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  promoContainer: {
    flexDirection: 'row',
    marginBottom: Layout.spacing.md,
  },
  promoInputContainer: {
    flex: 1,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.sm,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: Layout.spacing.sm,
  },
  promoInput: {
    fontSize: 14,
  },
  promoButton: {
    height: 40,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Layout.spacing.sm,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: Layout.spacing.sm,
    marginTop: Layout.spacing.sm,
    marginBottom: Layout.spacing.md,
    borderTopWidth: 1,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  checkoutButton: {
    marginTop: Layout.spacing.sm,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.xl,
  },
  shopButton: {
    width: 200,
  },
});
