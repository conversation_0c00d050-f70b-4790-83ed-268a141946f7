/**
 * Authentication API service for the CashyMall mobile app
 */
import { post, get, patch } from './index';
import { AUTH_ENDPOINTS, USER_ENDPOINTS } from './endpoints';
import { saveToken, clearTokens } from '../storage/tokenStorage';

// User interface
export interface User {
  id?: string;
  firstname?: string;
  lastname?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  profilePicture?: string;
  address?:string;
  city?:string;
  state?:string;
  zipCode?:string;
  country?:string;
  dateOfBirth?:string;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any; // Allow for additional properties
}
// Authentication response interface
export interface AuthResponse {
  token: string;
  user: User;
  message: string;
}

// Login credentials interface
interface LoginCredentials {
  email: string;
  password: string;
}

// Signup data interface
interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
}

/**
 * Login user with email and password
 * @param credentials Login credentials
 * @returns Login response or error
 */
export async function login(credentials: LoginCredentials) {
  const response = await post<AuthResponse>(
    AUTH_ENDPOINTS.LOGIN,
    credentials,
    { requiresAuth: false }
  );

  if (response.data && response.data.token) {
    // Save authentication token
    await saveToken({
      token: response.data.token,
      user: response.data.user
    });
  }

  return response;
}

/**
 * Register a new user
 * @param userData User registration data
 * @returns Registration response or error
 */
export async function signup(userData: SignupData) {
  // Ensure the field names match the API expectations
  const apiUserData = {
    firstName: userData.firstName,
    lastName: userData.lastName,
    email: userData.email,
    phoneNumber: userData.phoneNumber,
    password: userData.password
  };

  const response = await post<AuthResponse>(
    AUTH_ENDPOINTS.SIGNUP,
    apiUserData,
    { requiresAuth: false }
  );

  if (response.data && response.data.token) {
    // Save authentication token
    await saveToken({
      token: response.data.token,
      user: response.data.user
    });
  }

  return response;
}

/**
 * Logout user (local logout only - no API call)
 * @returns Success response
 */
export async function logout() {
  try {
    // Clear tokens from local storage
    await clearTokens();

    return {
      data: { message: 'Logged out successfully' },
      error: null,
      status: 200
    };
  } catch (error) {
    console.error('Local logout error:', error);
    throw error;
  }
}

/**
 * Request password reset
 * @param email User email
 * @returns Password reset response or error
 */
export async function forgotPassword(email: string) {
  return post(
    AUTH_ENDPOINTS.FORGOT_PASSWORD,
    { email },
    { requiresAuth: false }
  );
}

/**
 * Reset password with token
 * @param token Reset token
 * @param newPassword New password
 * @returns Password reset response or error
 */
export async function resetPassword(token: string, newPassword: string) {
  return post(
    AUTH_ENDPOINTS.RESET_PASSWORD,
    { token, newPassword },
    { requiresAuth: false }
  );
}

/**
 * Get user profile
 * @returns User profile or error
 */
export async function getUserProfile(id:string) {
  return get<User>(`${USER_ENDPOINTS.PROFILE}/${id}`);
}


export async function updateProfile(userData: User) {
  return patch(`${USER_ENDPOINTS.UPDATE_PROFILE}/${userData.id}`, userData);
}

export async function uploadProfilePicture(id: string, image: string) {
  return post(`${USER_ENDPOINTS.uploadImg}/${id}`, { image });

}

export async function updatePassword(oldPassword: string, newPassword: string) {
  return patch(USER_ENDPOINTS.updatePassword, { oldPassword, newPassword });
}

export async function updateAddress(id:string, addressData: any) {
  return patch(`${USER_ENDPOINTS.UPDATE_ADDRESS}/${id}`, addressData);
}


