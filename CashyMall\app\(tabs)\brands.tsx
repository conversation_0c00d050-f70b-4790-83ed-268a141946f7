import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
  ActivityIndicator,
} from 'react-native';
import Header from '../../components/Header';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getBrands } from '@/services/api/brand';
import {  getProductsByBrandId } from '@/services/api/product';


export default function BrandsScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const [brands, setBrands] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

 useEffect(() => {
  async function fetchBrands() {
    try {
      setLoading(true);
      const response = await getBrands({
        page: 0,
        size: 50,
        sortBy: 'name',
        direction: 'asc',
      });

      const rawBrands = response?.data?.data?.content || [];

      // Enrich each brand with productCount
      const enrichedBrands = await Promise.all(
        rawBrands.map(async (brand: any) => {
          try {
              const productsResponse = await getProductsByBrandId(brand.id);
      const productCount =  productsResponse.data.data.length;

      return {
        ...brand,
        productCount,
      };

            return { ...brand, productCount };
          } catch (error) {
            console.error(`Failed to get product count for brand ${brand.name}:`, error);
            return { ...brand, productCount: 0 };
          }
        })
      );

      setBrands(enrichedBrands);
    } catch (error) {
      console.error('Failed to fetch brands:', error);
    } finally {
      setLoading(false);
    }
  }

  fetchBrands();
}, []);

  const featuredBrands = brands.filter(brand => brand.featured);
  const allBrands = [...brands].sort((a, b) => a.name.localeCompare(b.name));

  const handleBrandPress = (brandId: string) => {
    router.push(`/shop?brand=${brandId}`);
  };

  const renderBrandItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[styles.brandCard, { backgroundColor: colors.cardBackground }]}
      onPress={() => handleBrandPress(item.id)}
    >
      <View style={styles.brandLogoContainer}>
        <Image
          source={{ uri: item.logo }}
          style={styles.brandLogo}
          resizeMode="contain"
        />
      </View>
      <Text style={[styles.brandName, { color: colors.text }]}>
        {item.name}
      </Text>
      <Text style={[styles.productCount, { color: colors.tabIconDefault }]}>
        {item.productCount} Products
      </Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={colors.tint} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="Brands" />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Featured Brands */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Featured Brands
          </Text>

          <View style={styles.featuredBrandsContainer}>
            {featuredBrands.map(brand => (
              <TouchableOpacity
                key={brand.id}
                style={[styles.featuredBrandCard, { backgroundColor: colors.cardBackground }]}
                onPress={() => handleBrandPress(brand.id)}
              >
                <View style={styles.featuredBrandLogoContainer}>
                  <Image
                    source={{ uri: brand.logo }}
                    style={styles.featuredBrandLogo}
                    resizeMode="contain"
                  />
                </View>
                <View style={styles.featuredBrandInfo}>
                  <Text style={[styles.featuredBrandName, { color: colors.text }]}>
                    {brand.name}
                  </Text>
                  <Text style={[styles.featuredProductCount, { color: colors.tabIconDefault }]}>
                    {brand.productCount} Products
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* All Brands */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            All Brands
          </Text>

          <FlatList
            data={allBrands}
            renderItem={renderBrandItem}
            keyExtractor={item => item.id}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={styles.brandsGrid}
            columnWrapperStyle={styles.brandRow}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: Layout.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  featuredBrandsContainer: {
    marginBottom: Layout.spacing.md,
  },
  featuredBrandCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.sm,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  featuredBrandLogoContainer: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  featuredBrandLogo: {
    width: '100%',
    height: '100%',
  },
  featuredBrandInfo: {
    flex: 1,
  },
  featuredBrandName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: Layout.spacing.xs,
  },
  featuredProductCount: {
    fontSize: 14,
  },
  brandsGrid: {
    paddingBottom: Layout.spacing.md,
  },
  brandRow: {
    justifyContent: 'space-between',
    marginBottom: Layout.spacing.md,
  },
  brandCard: {
    width: (Layout.window.width - (Layout.spacing.md * 3)) / 2,
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
      web: {
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  brandLogoContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  brandLogo: {
    width: '100%',
    height: '100%',
  },
  brandName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: Layout.spacing.xs,
    textAlign: 'center',
  },
  productCount: {
    fontSize: 14,
    textAlign: 'center',
  },
});
