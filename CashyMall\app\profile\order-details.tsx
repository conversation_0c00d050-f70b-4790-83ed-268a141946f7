import React, { useState, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  Image,
  ScrollView,
  Platform,
  ActivityIndicator
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { AuthContext } from '@/context/AuthContext';
import { getOrderDetails } from '@/services/api/orders';
import { Order } from '@/types/product';

export default function OrderDetailsScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useContext(AuthContext)!;
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        const response = await getOrderDetails(id as string);
        
        if (response.data?.data) {
          setOrder(response.data?.data);
          setError(null);
        } else {
          setError('Failed to fetch order details');
        }
      } catch (err: any) {
        setError(err?.message || 'An error occurred while fetching order details');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchOrderDetails();
    }
  }, [id]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return '#10b981'; // green
      case 'Shipped':
      case 'Out for Delivery':
        return '#3b82f6'; // blue
      case 'Processing':
        return '#f59e0b'; // amber
      case 'Cancelled':
        return '#ef4444'; // red
      case 'Order Placed':
        return '#6366f1'; // indigo
      default:
        return colors.text;
    }
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Order Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </View>
    );
  }

  if (!order) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Order Details</Text>
          <View style={styles.placeholder} />
        </View>
        
        <View style={styles.notFoundContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.tabIconDefault} />
          <Text style={[styles.notFoundTitle, { color: colors.text }]}>
            Order Not Found
          </Text>
          <Text style={[styles.notFoundSubtitle, { color: colors.tabIconDefault }]}>
            The order you're looking for doesn't exist or has been removed.
          </Text>
          <TouchableOpacity 
            style={[styles.backToOrdersButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/profile/orders')}
          >
            <Text style={styles.backToOrdersButtonText}>
              Back to Orders
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Order Details</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={[styles.orderHeader, { backgroundColor: colors.cardBackground }]}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderId, { color: colors.text }]}
              testID="order-id"
            >
              {order?.orderNumber || order?.id}
            </Text>
            <Text style={[styles.orderDate, { color: colors.tabIconDefault }]}>
              Placed on {formatDate(order?.orderDate || '')}
            </Text>
          </View>
          
          <View style={styles.statusContainer}>
            <View 
              style={[
                styles.statusDot, 
                { backgroundColor: getStatusColor(order?.status || '') }
              ]} 
            />
            <Text style={[styles.statusText, { color: getStatusColor(order?.status || '') }]}
              testID="order-status"
            >
              {order?.status}
            </Text>
          </View>
        </View>
        
        <View style={[styles.section, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}
            testID="order-items-title"
          >
            Order Items
          </Text>
          
          {order?.items?.map((item) => (
            <View key={item.id} style={styles.orderItem}>
              <Image 
                source={{ uri: JSON.parse(item.productSnapshot).imageUrl }} 
                style={styles.itemImage}
                resizeMode="cover"
                testID="order-item-image"
              />
              <View style={styles.itemDetails}>
                <Text style={[styles.itemName, { color: colors.text }]}
                  testID="order-item-name"
                >
                  {item.productName}
                </Text>
                <Text style={[styles.itemPrice, { color: colors.tabIconDefault }]}
                  testID="order-item-price"
                >
                  ${item.productPrice.toFixed(2)}
                </Text>
                <Text style={[styles.itemQuantity, { color: colors.tabIconDefault }]}
                  testID="order-item-quantity"
                >
                  Quantity: {item.quantity}
                </Text>
              </View>
            </View>
          ))}
        </View>
        
        <View style={[styles.section, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}
            testID="order-summary-title"
          >
            Order Summary
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.text }]}
              testID="order-subtotal-label"
            >
              Subtotal
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}
              testID="order-subtotal-value"
            >
              ${order?.subtotal.toFixed(2)}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.text }]}
              testID="order-shipping-label"
            >
              Shipping
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}
              testID="order-shipping-value"
            >
              {order?.shippingCost === 0 ? 'Free' : `$${order?.shippingCost.toFixed(2)}`}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.text }]}
              testID="order-tax-label"
            >
              Tax
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}
              testID="order-tax-value"
            >
              ${order?.tax.toFixed(2)}
            </Text>
          </View>
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={[styles.totalLabel, { color: colors.text }]}
              testID="order-total-label"
            >
              Total
            </Text>
            <Text style={[styles.totalValue, { color: colors.primary }]}
              testID="order-total-value"
            >
              ${order?.total.toFixed(2)}
            </Text>
          </View>
        </View>
        
        <View style={[styles.section, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}
            testID="shipping-address-title"
          >
            Shipping Address
          </Text>
          
          <Text style={[styles.addressText, { color: colors.text }]}
            testID="shipping-address-name"
          >
            {order?.customerName}
          </Text>
          <Text style={[styles.addressText, { color: colors.text }]}
            testID="shipping-address-street"
          >
            {order?.shippingAddress}
          </Text>
          <Text style={[styles.addressText, { color: colors.text }]}
            testID="shipping-address-city-state-zip"
          >
            {order?.shippingCity}, {order?.shippingState} {order?.shippingZipCode}
          </Text>
          <Text style={[styles.addressText, { color: colors.text }]}
            testID="shipping-address-country"
          >
            {order?.shippingCountry}
          </Text>
        </View>
        
        <View style={[styles.section, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}
            testID="payment-method-title"
          >
            Payment Method
          </Text>
          
          <Text style={[styles.paymentMethod, { color: colors.text }]}
            testID="payment-method-value"
          >
            {order?.paymentMethod || 'Not specified'}
          </Text>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={() => console.log('Download invoice')}
          >
            <Ionicons name="download-outline" size={16} color="#ffffff" />
            <Text style={styles.actionButtonText}>
              Download Invoice
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, styles.secondaryButton, { borderColor: colors.border }]}
            onPress={() => console.log('Contact support')}
          >
            <Ionicons name="chatbubble-outline" size={16} color={colors.primary} />
            <Text style={[styles.secondaryButtonText, { color: colors.primary }]}>
              Need Help?
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Layout.spacing.md,
    marginHorizontal: Layout.spacing.md,
    marginTop: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 14,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    padding: Layout.spacing.md,
    marginHorizontal: Layout.spacing.md,
    marginTop: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  orderItem: {
    flexDirection: 'row',
    marginBottom: Layout.spacing.md,
  },
  itemImage: {
    width: 70,
    height: 70,
    borderRadius: Layout.borderRadius.sm,
  },
  itemDetails: {
    flex: 1,
    marginLeft: Layout.spacing.md,
    justifyContent: 'center',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 14,
    marginBottom: 4,
  },
  itemQuantity: {
    fontSize: 14,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Layout.spacing.sm,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    marginTop: Layout.spacing.sm,
    paddingTop: Layout.spacing.sm,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addressText: {
    fontSize: 14,
    marginBottom: 4,
  },
  paymentMethod: {
    fontSize: 14,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.sm,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    marginRight: 0,
    marginLeft: Layout.spacing.sm,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  notFoundContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.lg,
  },
  notFoundTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xs,
  },
  notFoundSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  backToOrdersButton: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
  },
  backToOrdersButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
