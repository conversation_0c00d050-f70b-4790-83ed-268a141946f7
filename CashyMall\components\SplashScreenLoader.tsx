import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  useColorScheme,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

interface SplashScreenLoaderProps {
  size?: number;
  color?: string;
}

export default function SplashScreenLoader({
  size = 80,
  color
}: SplashScreenLoaderProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Use provided color or default to the primary color
  const loaderColor = color || colors.primary;

  // Animation values
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0.8)).current;
  const bounceValue = useRef(new Animated.Value(0)).current;
  const opacityValue = useRef(new Animated.Value(0.4)).current;

  useEffect(() => {
    // Create a looping rotation animation
    Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 2000,
        easing: Easing.bezier(0.45, 0, 0.55, 1), // Smoother rotation
        useNativeDriver: true,
      })
    ).start();

    // Create a pulsing animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.15,
          duration: 800,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 0.85,
          duration: 800,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Create a bouncing animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(bounceValue, {
          toValue: -10,
          duration: 1000,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(bounceValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.bounce,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Create a fading animation for the sparkle effect
    Animated.loop(
      Animated.sequence([
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityValue, {
          toValue: 0.4,
          duration: 1200,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  // Interpolate the spin value to rotate from 0 to 360 degrees
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      {/* Sparkle effects */}
      <Animated.View
        style={[
          styles.sparkle,
          styles.sparkleTopRight,
          { opacity: opacityValue }
        ]}
      >
        <Ionicons name="star" size={size/4} color="#8B4513" />
      </Animated.View>

      <Animated.View
        style={[
          styles.sparkle,
          styles.sparkleBottomLeft,
          { opacity: opacityValue }
        ]}
      >
        <Ionicons name="star" size={size/4} color="#8B4513" />
      </Animated.View>

      {/* Main cart icon */}
      <Animated.View
        style={[
          styles.loaderContainer,
          {
            transform: [
              { rotate: spin },
              { scale: scaleValue },
              { translateY: bounceValue }
            ],
          },
        ]}
      >
        <Ionicons name="cart" size={size} color="#8B4513" />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 120,
    height: 120,
    position: 'relative',
  },
  loaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0)',
   
  },
  sparkle: {
    position: 'absolute',
    zIndex: 10,
  },
  sparkleTopRight: {
    top: 0,
    right: 0,
  },
  sparkleBottomLeft: {
    bottom: 10,
    left: 0,
  },
});
