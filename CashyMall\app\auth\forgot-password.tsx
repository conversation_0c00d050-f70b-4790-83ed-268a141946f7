import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Input from '../../components/Input';
import Button from '../../components/Button';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [errors, setErrors] = useState<{ email?: string }>({});
  
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;
    
    // Validate email
    if (!email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };

  const handleResetPassword = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would call your API to send a password reset email
      // const response = await api.sendPasswordResetEmail(email);
      
      setEmailSent(true);
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert(
        'Error',
        'There was an error sending the password reset email. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.logoContainer}>
          <Image 
            source={require('../../assets/images/logo.png')} 
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        
        <Text style={[styles.title, { color: colors.text }]}>Reset Password</Text>
        
        {!emailSent ? (
          <View style={styles.formContainer}>
            <Text style={[styles.description, { color: colors.text }]}>
              Enter your email address and we'll send you instructions to reset your password.
            </Text>
            
            <Input
              label="Email Address"
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              value={email}
              onChangeText={setEmail}
              error={errors.email}
              leftIcon={<Ionicons name="mail-outline" size={20} color={colors.tabIconDefault} />}
            />
            
            <Button
              title="Send Reset Link"
              onPress={handleResetPassword}
              isLoading={isLoading}
              fullWidth
              style={styles.resetButton}
            />
          </View>
        ) : (
          <View style={styles.successContainer}>
            <View style={styles.iconContainer}>
              <Ionicons name="mail" size={64} color={colors.primary} />
            </View>
            
            <Text style={[styles.successTitle, { color: colors.text }]}>
              Check Your Email
            </Text>
            
            <Text style={[styles.successDescription, { color: colors.text }]}>
              We've sent password reset instructions to:
            </Text>
            
            <Text style={[styles.emailText, { color: colors.primary }]}>
              {email}
            </Text>
            
            <Text style={[styles.successNote, { color: colors.text }]}>
              If you don't see the email in your inbox, please check your spam folder.
            </Text>
            
            <Button
              title="Back to Login"
              onPress={() => router.replace('/auth/login')}
              fullWidth
              style={styles.backToLoginButton}
            />
          </View>
        )}
        
        <View style={styles.loginContainer}>
          <Text style={[styles.loginText, { color: colors.text }]}>
            Remember your password?
          </Text>
          <TouchableOpacity onPress={() => router.replace('/auth/login')}>
            <Text style={[styles.loginLink, { color: colors.primary }]}>
              Log in
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
    alignSelf: 'flex-start',
    marginBottom: Layout.spacing.md,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
  },
  logo: {
    width: 150,
    height: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  formContainer: {
    marginBottom: Layout.spacing.xl,
  },
  resetButton: {
    marginTop: Layout.spacing.md,
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: Layout.spacing.xl,
  },
  iconContainer: {
    marginBottom: Layout.spacing.lg,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  successDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Layout.spacing.sm,
  },
  emailText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  successNote: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  backToLoginButton: {
    marginTop: Layout.spacing.md,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: Layout.spacing.md,
  },
  loginText: {
    fontSize: 14,
    marginRight: Layout.spacing.xs,
  },
  loginLink: {
    fontSize: 14,
    fontWeight: '600',
  },
});
