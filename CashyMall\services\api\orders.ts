import { get } from ".";
import { GetPagedParams, ORDER_ENDPOINTS } from "./endpoints";


/**
 * Get all oeders
 * @returns List of oeders
 */

export async function getOrders(params: GetPagedParams = {}, customerId: string) {
  const query = new URLSearchParams({
    page: String(params.page ?? 0),
    size: String(params.size ?? 10),
    sortBy: params.sortBy ?? 'name',
    direction: params.direction ?? 'asc',
  });

  const response = await get(`${ORDER_ENDPOINTS.MYLIST}/${customerId}?${query.toString()}`);
  return response;
}

/**
 * Get order details by ID
 * @param orderId Order ID
 * @returns Order details
 */
export async function getOrderDetails(orderId: string | number) {
  const response = await get(`${ORDER_ENDPOINTS.DETAILS}/${orderId}`);
  return response;
}