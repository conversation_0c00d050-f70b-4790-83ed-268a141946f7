import { Brand } from "@/types/product";
import { get } from ".";
import { BRAND_ENDPOINTS, GetPagedParams } from "./endpoints";

/**
 * Get all brands
 * @returns List of brands
 */

export async function getBrands(params: GetPagedParams = {}) {
  const query = new URLSearchParams({
    page: String(params.page ?? 0),
    size: String(params.size ?? 10),
    sortBy: params.sortBy ?? 'name',
    direction: params.direction ?? 'asc',
  });

  const response = await get(`${BRAND_ENDPOINTS.LIST}?${query.toString()}`);
  return response;
}