import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  useColorScheme,
  Dimensions,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import Button from '../../components/Button';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getProductDetails } from '../../services/api/product';
import { addToCart } from '../../services/api/cart';
import { Product, ProductDetailResponse } from '../../types/product';
import { useCartWishlist } from '../../context/CartWishlistContext';

// Mock product data
const products = {
  '1': {
    id: '1',
    name: 'Wireless Headphones',
    price: 129.99,
    discountPrice: 99.99,
    imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1546435770-a3e426bf472b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1165&q=80',
      'https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=768&q=80'
    ],
    description: 'Experience premium sound quality with our wireless headphones. Featuring noise cancellation technology, long battery life, and comfortable ear cushions for extended listening sessions.',
    features: [
      'Active Noise Cancellation',
      '30-hour battery life',
      'Bluetooth 5.0 connectivity',
      'Built-in microphone for calls',
      'Foldable design for easy storage'
    ],
    rating: 4.5,
    reviewCount: 128,
    inStock: true,
    category: 'electronics'
  },
  '2': {
    id: '2',
    name: 'Smart Watch',
    price: 199.99,
    imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1099&q=80',
    images: [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1099&q=80',
      'https://images.unsplash.com/photo-1579586337278-3befd40fd17a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1172&q=80',
      'https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=627&q=80'
    ],
    description: 'Stay connected and track your fitness with our advanced smart watch. Monitor your heart rate, count steps, receive notifications, and more with this sleek and stylish wearable device.',
    features: [
      'Heart rate monitoring',
      'Step and calorie tracking',
      'Water resistant up to 50m',
      'Notification alerts',
      '7-day battery life'
    ],
    rating: 4.2,
    reviewCount: 95,
    inStock: true,
    category: 'electronics'
  },
  '3': {
    id: '3',
    name: 'Premium Leather Wallet',
    price: 59.99,
    discountPrice: 39.99,
    imageUrl: 'https://images.unsplash.com/photo-1627123424574-724758594e93?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1171&q=80',
    images: [
      'https://images.unsplash.com/photo-1627123424574-724758594e93?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1171&q=80',
      'https://images.unsplash.com/photo-1606503825008-909a67e63c3d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1611010344444-5f9e4d86a6e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1964&q=80'
    ],
    description: 'Crafted from genuine leather, this premium wallet combines style and functionality. With multiple card slots, a bill compartment, and RFID protection, it is the perfect accessory for the modern individual.',
    features: [
      'Genuine leather construction',
      'Multiple card slots',
      'RFID protection',
      'Slim profile design',
      'Durable stitching'
    ],
    rating: 4.7,
    reviewCount: 83,
    inStock: true,
    category: 'clothes'
  },
  '4': {
    id: '4',
    name: 'Stylish Sunglasses',
    price: 79.99,
    imageUrl: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80',
    images: [
      'https://images.unsplash.com/photo-1572635196237-14b3f281503f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80',
      'https://images.unsplash.com/photo-1511499767150-a48a237f0083?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80',
      'https://images.unsplash.com/photo-1577803645773-f96470509666?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
    ],
    description: 'Protect your eyes in style with these fashionable sunglasses. Featuring UV protection, polarized lenses, and a comfortable fit, these sunglasses are perfect for any outdoor activity.',
    features: [
      'UV400 protection',
      'Polarized lenses',
      'Lightweight frame',
      'Scratch-resistant coating',
      'Includes protective case'
    ],
    rating: 4.3,
    reviewCount: 56,
    inStock: true,
    category: 'clothes'
  },
  '5': {
    id: '5',
    name: 'Coffee Maker',
    price: 149.99,
    discountPrice: 129.99,
    imageUrl: 'https://images.unsplash.com/photo-1517668808822-9ebb02f2a0e6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    images: [
      'https://images.unsplash.com/photo-1517668808822-9ebb02f2a0e6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1572119865084-43c285814d63?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1610889556528-9a770e32642f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1115&q=80'
    ],
    description: 'Brew the perfect cup of coffee with our premium coffee maker. With programmable settings, a built-in grinder, and a thermal carafe, you can enjoy fresh, hot coffee exactly how you like it.',
    features: [
      'Programmable brewing',
      'Built-in grinder',
      'Thermal carafe',
      'Water filtration system',
      'Auto shut-off feature'
    ],
    rating: 4.6,
    reviewCount: 112,
    inStock: true,
    category: 'home-kitchen'
  },
  '6': {
    id: '6',
    name: 'Makeup Brush Set',
    price: 49.99,
    imageUrl: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    images: [
      'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1599733594230-6b823276abcc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1631214524020-3c8be9cd4711?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
    ],
    description: 'Achieve a flawless makeup application with our professional-grade brush set. Includes a variety of brushes for foundation, powder, blush, eyeshadow, and more, all made with soft, synthetic bristles.',
    features: [
      '12-piece brush set',
      'Soft synthetic bristles',
      'Ergonomic handles',
      'Travel-friendly case',
      'Cruelty-free'
    ],
    rating: 4.4,
    reviewCount: 78,
    inStock: true,
    category: 'beauty'
  },
  '7': {
    id: '7',
    name: 'Smartphone',
    price: 899.99,
    discountPrice: 799.99,
    imageUrl: 'https://images.unsplash.com/photo-1598327105666-5b89351aff97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1227&q=80',
    images: [
      'https://images.unsplash.com/photo-1598327105666-5b89351aff97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1227&q=80',
      'https://images.unsplash.com/photo-1605236453806-6ff36851218e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80',
      'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80'
    ],
    description: 'Stay connected with our latest smartphone. Featuring a high-resolution display, powerful processor, advanced camera system, and long-lasting battery, this phone has everything you need.',
    features: [
      '6.5-inch OLED display',
      'Triple camera system',
      '128GB storage',
      'All-day battery life',
      'Water and dust resistant'
    ],
    rating: 4.8,
    reviewCount: 203,
    inStock: true,
    category: 'electronics'
  },
  '8': {
    id: '8',
    name: 'Kitchen Knife Set',
    price: 129.99,
    imageUrl: 'https://images.unsplash.com/photo-1593618998160-e34014e67546?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    images: [
      'https://images.unsplash.com/photo-1593618998160-e34014e67546?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1566454825481-9c31bd88eac9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80',
      'https://images.unsplash.com/photo-1591349884490-4a66f8d2ef2e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
    ],
    description: "Elevate your cooking with our premium kitchen knife set. Includes chef's knife, bread knife, utility knife, paring knife, and kitchen shears, all made with high-carbon stainless steel for precision cutting.",
    features: [
      'High-carbon stainless steel',
      '5-piece set with block',
      'Ergonomic handles',
      'Precision-honed edges',
      'Dishwasher safe'
    ],
    rating: 4.5,
    reviewCount: 91,
    inStock: true,
    category: 'home-kitchen'
  }
};

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams();
  const productId = typeof id === 'string' ? id : '1';

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Use context for real-time updates
  const { addToCartCount } = useCartWishlist();

  // Fetch product details from API
  useEffect(() => {
    const fetchProductDetails = async () => {
      setLoading(true);
      try {
        const response = await getProductDetails(productId);
        console.log('Product Details Response:', JSON.stringify(response, null, 2));

        if (response.data?.data) {
          setProduct(response.data.data);
          setError(null);
        } else {
          setError('Invalid product details format received from API');
          console.error('Invalid product details format:', response);
        }
      } catch (err) {
        setError('An error occurred while fetching product details');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [productId]);

  const handleIncrement = () => {
    setQuantity(quantity + 1);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleAddToCart = async () => {
    if (!product) return;

    try {
      const response = await addToCart(product.id.toString(), quantity);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }

      // Update cart count immediately with the selected quantity
      addToCartCount(quantity);
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  // Show loading state
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header showBack />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading product details...
          </Text>
        </View>
      </View>
    );
  }

  // Show error state
  if (error || !product) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header showBack />
        <View style={styles.notFoundContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={[styles.notFoundText, { color: colors.text }]}>
            {error || 'Product not found'}
          </Text>
          <Button
            title="Go Back to Shop"
            onPress={() => router.push('/shop')}
            style={styles.notFoundButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header showBack />

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Product Images */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: product.imageUrl }}
            style={styles.mainImage}
            resizeMode="cover"
          />

          <View style={styles.thumbnailsContainer}>
            {/* Main image */}
            <TouchableOpacity
              key="main"
              style={[
                styles.thumbnailButton,
                selectedImageIndex === 0 && { borderColor: colors.primary }
              ]}
              onPress={() => setSelectedImageIndex(0)}
            >
              <Image
                source={{ uri: product.imageUrl }}
                style={styles.thumbnailImage}
                resizeMode="cover"
              />
            </TouchableOpacity>

            {/* Additional images if available */}
            {product.additionalImages && product.additionalImages.map((image, index) => (
              <TouchableOpacity
                key={index + 1}
                style={[
                  styles.thumbnailButton,
                  selectedImageIndex === index + 1 && { borderColor: colors.primary }
                ]}
                onPress={() => setSelectedImageIndex(index + 1)}
              >
                <Image
                  source={{ uri: image }}
                  style={styles.thumbnailImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Product Info */}
        <View style={styles.infoContainer}>
          <Text style={[styles.productName, { color: colors.text }]}>
            {product.name}
          </Text>

          <View style={styles.ratingContainer}>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Ionicons
                  key={star}
                  name={star <= Math.floor(product.averageRating || 0) ? "star" : star <= (product.averageRating || 0) ? "star-half" : "star-outline"}
                  size={16}
                  color="#FFD700"
                  style={styles.starIcon}
                />
              ))}
            </View>
            <Text style={[styles.reviewCount, { color: colors.tabIconDefault }]}>
              ({product.reviewCount} reviews)
            </Text>
          </View>

          <View style={styles.priceContainer}>
            {product.discountPrice ? (
              <>
                <Text style={[styles.discountPrice, { color: colors.primary }]}>
                  ${product.discountPrice.toFixed(2)}
                </Text>
                <Text style={[styles.originalPrice, { color: colors.tabIconDefault }]}>
                  ${product.price.toFixed(2)}
                </Text>
              </>
            ) : (
              <Text style={[styles.price, { color: colors.text }]}>
                ${product.price.toFixed(2)}
              </Text>
            )}
          </View>

          <Text style={[styles.description, { color: colors.text }]}>
            {product.description}
          </Text>

          {product.features && (
            <>
              <Text style={[styles.featuresTitle, { color: colors.text }]}>
                Key Features
              </Text>

              <View style={styles.featuresList}>
                {product.features.split(',').map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <Ionicons name="checkmark-circle" size={18} color={colors.primary} />
                    <Text style={[styles.featureText, { color: colors.text }]}>
                      {feature.trim()}
                    </Text>
                  </View>
                ))}
              </View>
            </>
          )}

          <View style={styles.actionsContainer}>
            <View style={[styles.quantityContainer, { borderColor: colors.border }]}>
              <TouchableOpacity
                style={[styles.quantityButton, { borderRightColor: colors.border }]}
                onPress={handleDecrement}
                disabled={quantity <= 1}
              >
                <Ionicons
                  name="remove"
                  size={20}
                  color={quantity <= 1 ? colors.tabIconDefault : colors.text}
                />
              </TouchableOpacity>

              <Text style={[styles.quantity, { color: colors.text }]}>
                {quantity}
              </Text>

              <TouchableOpacity
                style={[styles.quantityButton, { borderLeftColor: colors.border }]}
                onPress={handleIncrement}
              >
                <Ionicons name="add" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>

            <Button
              title="Add to Cart"
              style={styles.addToCartButton}
              onPress={handleAddToCart}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
  },
  mainImage: {
    width: '100%',
    height: 300,
  },
  thumbnailsContainer: {
    flexDirection: 'row',
    padding: Layout.spacing.md,
    justifyContent: 'center',
  },
  thumbnailButton: {
    width: 60,
    height: 60,
    borderRadius: Layout.borderRadius.sm,
    marginHorizontal: Layout.spacing.xs,
    borderWidth: 2,
    borderColor: 'transparent',
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    padding: Layout.spacing.md,
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  starsContainer: {
    flexDirection: 'row',
  },
  starIcon: {
    marginRight: 2,
  },
  reviewCount: {
    fontSize: 14,
    marginLeft: Layout.spacing.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  discountPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: Layout.spacing.sm,
  },
  originalPrice: {
    fontSize: 18,
    textDecorationLine: 'line-through',
  },
  description: {
    fontSize: 14,
    lineHeight: 22,
    marginBottom: Layout.spacing.lg,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: Layout.spacing.sm,
  },
  featuresList: {
    marginBottom: Layout.spacing.lg,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.xs,
  },
  featureText: {
    fontSize: 14,
    marginLeft: Layout.spacing.xs,
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    overflow: 'hidden',
    marginRight: Layout.spacing.md,
  },
  quantityButton: {
    padding: Layout.spacing.sm,
    borderRightWidth: 1,
    borderLeftWidth: 1,
  },
  quantity: {
    paddingHorizontal: Layout.spacing.md,
    fontSize: 16,
    fontWeight: '500',
  },
  addToCartButton: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  loadingText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
  },
  notFoundContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  notFoundText: {
    fontSize: 18,
    fontWeight: '500',
    marginVertical: Layout.spacing.md,
  },
  notFoundButton: {
    marginTop: Layout.spacing.md,
  },
});
