import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Animated,
  Dimensions,
  useColorScheme,
} from 'react-native';
import Colors from '../constants/Colors';
import SplashScreenLoader from './SplashScreenLoader';

interface AnimatedSplashScreenProps {
  onAnimationComplete: () => void;
}

const { width, height } = Dimensions.get('window');

export default function AnimatedSplashScreen({ onAnimationComplete }: AnimatedSplashScreenProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const logoFadeAnim = useRef(new Animated.Value(0)).current;
  const logoScaleAnim = useRef(new Animated.Value(0.4)).current;
  const textFadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start animations in sequence
    Animated.sequence([
      // First, fade in the background
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),

      // Then, animate the logo
      Animated.parallel([
        Animated.timing(logoFadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.spring(logoScaleAnim, {
          toValue: 1,
          friction: 6,
          tension: 35,
          useNativeDriver: true,
        }),
      ]),

      // Finally, fade in the text
      Animated.timing(textFadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),

      // Wait a bit before completing
      Animated.delay(1200),
    ]).start(() => {
      // Animation complete callback
      onAnimationComplete();
    });
  }, []);

  // Use a gradient-like effect with a more friendly color
  const backgroundColor = colorScheme === 'dark' ? '#d8a8a8' : '#e5c0c0';
  const textColor = '#8B4513';

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor,
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      {/* Decorative elements - circles */}
      <View style={styles.decorCircle1} />
      <View style={styles.decorCircle2} />
      <View style={styles.decorCircle3} />

      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: logoFadeAnim,
            transform: [{ scale: logoScaleAnim }]
          }
        ]}
      >
        <SplashScreenLoader size={120} color="#ffffff" />
        <Image
          source={require('../assets/images/logoCM.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>

      

      {/* Footer text */}
      <Animated.Text
        style={[
          styles.footerText,
          {
            opacity: textFadeAnim
          }
        ]}
      >
        Happy Shopping!
      </Animated.Text>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ccefef',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    overflow: 'hidden',
  },
  // Decorative elements
  decorCircle1: {
    position: 'absolute',
    width: 220,
    height: 220,
    borderRadius: 110,
    backgroundColor: '#e97e7e',
    top: -60,
    right: -60,
    opacity: 0.25,
  },
  decorCircle2: {
    position: 'absolute',
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: '#e9a07e',
    bottom: 60,
    left: -40,
    opacity: 0.2,
  },
  decorCircle3: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#212529',
    top: '40%',
    left: '70%',
    opacity: 0.15,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 240,
    height: 240,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
  },
  textContainer: {
    alignItems: 'center',
    paddingVertical: 18,
  },
  title: {
    fontSize: 38,
    fontWeight: 'bold',
    marginBottom: 10,
    letterSpacing: 1.2,
    color: '#212529',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: 20,
    opacity: 0.95,
    letterSpacing: 0.8,
    fontWeight: '500',
    color: '#212529',
  },
  footerText: {
    position: 'absolute',
    bottom: 50,
    fontSize: 18,
    color: '#e97e7e',
    fontWeight: '600',
    letterSpacing: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});

