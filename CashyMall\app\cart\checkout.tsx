import React, { useContext, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  useColorScheme,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Modal,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { placeOrder } from "../../services/api/order";
import type { OrderRequest } from "../../services/api/order";
import Button from "../../components/Button";
import Input from "../../components/Input";
import Colors from "../../constants/Colors";
import Layout from "../../constants/Layout";
import { getCart } from "../../services/api/cart";
import type { CartItem as CartItemType } from "../../types/product";
import { AuthContext } from "../../context/AuthContext";
import { ALERT_TYPE, Toast } from "react-native-alert-notification";

export default function CheckoutScreen() {
  const colorScheme = useColorScheme() || "light";
  const colors = Colors[colorScheme];
  const router = useRouter();
  const { user } = useContext(AuthContext)!;

  const [cartItems, setCartItems] = useState<CartItemType[]>([]);
  const [loadingCart, setLoadingCart] = useState(true);

  // Form state initialized with user data
  const [firstName, setFirstName] = useState(user?.firstname || "");
  const [lastName, setLastName] = useState(user?.lastname || "");
  const [email, setEmail] = useState(user?.email || "");
  const [phone, setPhone] = useState(user?.phoneNumber || "");
  const [address, setAddress] = useState(user?.address || "");
  const [city, setCity] = useState(user?.city || "");
  const [state, setState] = useState(user?.state || "");
  const [zipCode, setZipCode] = useState(user?.zipCode || "");
  const [country, setCountry] = useState(user?.country || "");
  const [paymentMethod, setPaymentMethod] = useState("credit_card");
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  // Form validation
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  }>({});

  // Fetch cart items
  useEffect(() => {
    const fetchCartItems = async () => {
      try {
        const response = await getCart();
        if (response.data && Array.isArray(response.data.items)) {
          setCartItems(response.data.items);
        }
      } catch (err) {
        console.error("Error fetching cart:", err);
        Alert.alert("Error", "Failed to load cart items");
      } finally {
        setLoadingCart(false);
      }
    };

    fetchCartItems();
  }, []);

  // Calculate order summary
  const calculateOrderSummary = () => {
    const subtotal = cartItems.reduce((total, item) => {
      return total + item.subtotal;
    }, 0);

    const totalDiscount = cartItems.reduce((total, item) => {
      const regularPrice = item.productPrice;
      const discountedPrice =
        item.productPrice - (item.productPrice * item.discountPrice) / 100;
      const itemDiscount = (regularPrice - discountedPrice) * item.quantity;
      return total + itemDiscount;
    }, 0);
    const shipping = 0; // $10 shipping fee

    const total = subtotal + shipping;
    console.log("discount", totalDiscount);
    return {
      subtotal: subtotal.toFixed(2),
      shipping: shipping.toFixed(2),
      discount: totalDiscount,
      total: total.toFixed(2),
    };
  };

  const orderSummary = calculateOrderSummary();

  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;

    if (!firstName.trim()) {
      newErrors.firstName = "First name is required";
      isValid = false;
    }

    if (!lastName.trim()) {
      newErrors.lastName = "Last name is required";
      isValid = false;
    }

    if (!email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email is invalid";
      isValid = false;
    }

    if (!phone.trim()) {
      newErrors.phone = "Phone number is required";
      isValid = false;
    }

    if (!address.trim()) {
      newErrors.address = "Address is required";
      isValid = false;
    }

    if (!city.trim()) {
      newErrors.city = "City is required";
      isValid = false;
    }

    if (!state.trim()) {
      newErrors.state = "State is required";
      isValid = false;
    }

    if (!zipCode.trim()) {
      newErrors.zipCode = "ZIP code is required";
      isValid = false;
    }

    if (!country.trim()) {
      newErrors.country = "Country is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };
  const handlePlaceOrder = async () => {
    if (!validateForm()) {
      console.log("Form validation failed");
      return;
    }

    try {
      setIsLoading(true);

      const orderRequest = {
        customerId: Number(user?.id) || 0,
        items: cartItems.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          color: item.color || "",
          size: item.size || "",
        })),
        tax: 0,
        shippingCost: 0,
        discount: orderSummary.discount,
        total: parseFloat(orderSummary.total),
        shippingAddress: address,
        shippingCity: city,
        shippingState: state,
        shippingZipCode: zipCode,
        shippingCountry: country,
        billingAddress: address,
        billingCity: city,
        billingState: state,
        billingZipCode: zipCode,
        billingCountry: country,
        paymentMethod: paymentMethod,
        paymentTransactionId: `TRANSACTION${Date.now()}`,
        notes: "",
      };

      console.log("Placing order:", orderRequest);
      const response = await placeOrder(orderRequest);
      console.log("Order response:", response);

      // Show success alert with auto-dismiss after 2 seconds
   
        Toast.show({
          type: ALERT_TYPE.SUCCESS,
          title: 'Order Placed Successfully!',
          textBody: 'Your order has been confirmed. Thank you for shopping with us!',
        })
       router.push("/shop");
      
      console.log("Order22 response:", response);
      if (response.error) {
        throw new Error(response.error);
      }
    } catch (error) {
      console.error("Checkout error:", error);
    Toast.show({
  type: ALERT_TYPE.DANGER,
  title: 'Order Failed ❌',
  textBody: 'Something went wrong while placing your order. Please try again.',
});
    } finally {
      setIsLoading(false);
    }
  };

  

  if (loadingCart) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Checkout
          </Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <Text style={[styles.emptyText, { color: colors.text }]}>
            Loading cart items...
          </Text>
        </View>
      </View>
    );
  }

  if (cartItems.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Checkout
          </Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.emptyContainer}>
          <Ionicons
            name="cart-outline"
            size={64}
            color={colors.tabIconDefault}
          />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            Your cart is empty
          </Text>
          <Text
            style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}
          >
            Add some items to your cart before checking out.
          </Text>
          <Button
            title="Go Shopping"
            style={styles.shopButton}
            onPress={() => router.push("/shop")}
          />
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Checkout
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Shipping Information */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Shipping Information
          </Text>

          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Input
                label="First Name"
                placeholder="Enter first name"
                value={firstName}
                onChangeText={setFirstName}
                error={errors.firstName}
                containerStyle={styles.input}
              />
            </View>
            <View style={styles.formColumn}>
              <Input
                label="Last Name"
                placeholder="Enter last name"
                value={lastName}
                onChangeText={setLastName}
                error={errors.lastName}
                containerStyle={styles.input}
              />
            </View>
          </View>

          <Input
            label="Email"
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
            error={errors.email}
          />

          <Input
            label="Phone Number"
            placeholder="Enter phone number"
            keyboardType="phone-pad"
            value={phone}
            onChangeText={setPhone}
            error={errors.phone}
          />

          <Input
            label="Address"
            placeholder="Enter street address"
            value={address}
            onChangeText={setAddress}
            error={errors.address}
          />

          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Input
                label="City"
                placeholder="Enter city"
                value={city}
                onChangeText={setCity}
                error={errors.city}
                containerStyle={styles.input}
              />
            </View>
            <View style={styles.formColumn}>
              <Input
                label="State/Province"
                placeholder="Enter state"
                value={state}
                onChangeText={setState}
                error={errors.state}
                containerStyle={styles.input}
              />
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Input
                label="ZIP Code"
                placeholder="Enter ZIP code"
                keyboardType="numeric"
                value={zipCode}
                onChangeText={setZipCode}
                error={errors.zipCode}
                containerStyle={styles.input}
              />
            </View>
            <View style={styles.formColumn}>
              <Input
                label="Country"
                placeholder="Enter country"
                value={country}
                onChangeText={setCountry}
                error={errors.country}
                containerStyle={styles.input}
              />
            </View>
          </View>
        </View>

        {/* Payment Method */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Payment Method
          </Text>

          <View style={styles.paymentOptions}>
            <TouchableOpacity
              style={[
                styles.paymentOption,
                paymentMethod === "credit_card" && [
                  styles.selectedPaymentOption,
                  { borderColor: colors.primary },
                ],
              ]}
              onPress={() => setPaymentMethod("credit_card")}
            >
              <Ionicons
                name="card-outline"
                size={24}
                color={
                  paymentMethod === "credit_card"
                    ? colors.primary
                    : colors.tabIconDefault
                }
              />
              <Text
                style={[
                  styles.paymentOptionText,
                  {
                    color:
                      paymentMethod === "credit_card"
                        ? colors.primary
                        : colors.text,
                  },
                ]}
              >
                Credit Card
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.paymentOption,
                paymentMethod === "paypal" && [
                  styles.selectedPaymentOption,
                  { borderColor: colors.primary },
                ],
              ]}
              onPress={() => setPaymentMethod("paypal")}
            >
              <Ionicons
                name="logo-paypal"
                size={24}
                color={
                  paymentMethod === "paypal"
                    ? colors.primary
                    : colors.tabIconDefault
                }
              />
              <Text
                style={[
                  styles.paymentOptionText,
                  {
                    color:
                      paymentMethod === "paypal" ? colors.primary : colors.text,
                  },
                ]}
              >
                PayPal
              </Text>
            </TouchableOpacity>
          </View>

          {/* Payment details would go here - omitted for brevity */}
        </View>

        {/* Order Summary */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Order Summary
          </Text>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.text }]}>
              Subtotal
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              ${orderSummary.subtotal}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.text }]}>
              Shipping
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              ${orderSummary.shipping}
            </Text>
          </View>

          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={[styles.totalLabel, { color: colors.text }]}>
              Total
            </Text>
            <Text style={[styles.totalValue, { color: colors.primary }]}>
              ${orderSummary.total}
            </Text>
          </View>
        </View>
        <Modal
          visible={showConfirmationModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowConfirmationModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Confirm Order</Text>
              <Text style={styles.modalMessage}>
                Are you sure you want to place this order?
              </Text>

              <View style={styles.modalButtons}>
                <Button
                  title="Cancel"
                  onPress={() => setShowConfirmationModal(false)}
                />
                <Button
                  title="Confirm"
                  onPress={() => {
                    setShowConfirmationModal(false);
                    handlePlaceOrder();
                  }}
                />
              </View>
            </View>
          </View>
        </Modal>
        {/* Place Order Button */}
        <View style={styles.buttonContainer}>
          <Button
            title="Place Order"
            onPress={() => setShowConfirmationModal(true)}
            // isLoading={isLoading}
            fullWidth
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: Layout.spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: Layout.spacing.xl,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
  },
  shopButton: {
    width: 200,
    marginTop: Layout.spacing.lg,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: Layout.spacing.md,
  },
  formRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  formColumn: {
    flex: 1,
    marginRight: Layout.spacing.sm,
  },
  input: {
    marginRight: Layout.spacing.sm,
  },
  paymentOptions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  paymentOption: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: Layout.spacing.md,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.sm,
  },
  selectedPaymentOption: {
    borderWidth: 2,
  },
  paymentOptionText: {
    marginLeft: Layout.spacing.sm,
    fontWeight: "500",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: Layout.spacing.sm,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    marginTop: Layout.spacing.sm,
    paddingTop: Layout.spacing.md,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "bold",
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
  },
  buttonContainer: {
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,
    width: "80%",
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  modalMessage: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
});
