import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Input from '../../components/Input';
import Button from '../../components/Button';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';

export default function ResetPasswordScreen() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const [errors, setErrors] = useState<{ 
    password?: string;
    confirmPassword?: string;
  }>({});
  
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();
  const { token } = useLocalSearchParams();

  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;
    
    // Validate password
    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }
    
    // Validate confirm password
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
      isValid = false;
    } else if (confirmPassword !== password) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };

  const handleResetPassword = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would call your API to reset the password
      // const response = await api.resetPassword(token, password);
      
      setResetSuccess(true);
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert(
        'Error',
        'There was an error resetting your password. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.logoContainer}>
          <Image 
            source={require('../../assets/images/logo.png')} 
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        
        <Text style={[styles.title, { color: colors.text }]}>Reset Password</Text>
        
        {!resetSuccess ? (
          <View style={styles.formContainer}>
            <Text style={[styles.description, { color: colors.text }]}>
              Enter your new password below.
            </Text>
            
            <Input
              label="New Password"
              placeholder="Enter new password"
              secureTextEntry
              isPassword
              value={password}
              onChangeText={setPassword}
              error={errors.password}
              leftIcon={<Ionicons name="lock-closed-outline" size={20} color={colors.tabIconDefault} />}
            />
            
            <Input
              label="Confirm Password"
              placeholder="Confirm new password"
              secureTextEntry
              isPassword
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              error={errors.confirmPassword}
              leftIcon={<Ionicons name="lock-closed-outline" size={20} color={colors.tabIconDefault} />}
            />
            
            <Button
              title="Reset Password"
              onPress={handleResetPassword}
              isLoading={isLoading}
              fullWidth
              style={styles.resetButton}
            />
          </View>
        ) : (
          <View style={styles.successContainer}>
            <View style={styles.iconContainer}>
              <Ionicons name="checkmark-circle" size={64} color={colors.primary} />
            </View>
            
            <Text style={[styles.successTitle, { color: colors.text }]}>
              Password Reset Successful
            </Text>
            
            <Text style={[styles.successDescription, { color: colors.text }]}>
              Your password has been reset successfully. You can now log in with your new password.
            </Text>
            
            <Button
              title="Log In"
              onPress={() => router.replace('/auth/login')}
              fullWidth
              style={styles.loginButton}
            />
          </View>
        )}
        
        <View style={styles.helpContainer}>
          <Text style={[styles.helpText, { color: colors.text }]}>
            Need help?
          </Text>
          <TouchableOpacity>
            <Text style={[styles.helpLink, { color: colors.primary }]}>
              Contact Support
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
    alignSelf: 'flex-start',
    marginBottom: Layout.spacing.md,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
  },
  logo: {
    width: 150,
    height: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  formContainer: {
    marginBottom: Layout.spacing.xl,
  },
  resetButton: {
    marginTop: Layout.spacing.md,
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: Layout.spacing.xl,
  },
  iconContainer: {
    marginBottom: Layout.spacing.lg,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.md,
  },
  successDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  loginButton: {
    marginTop: Layout.spacing.md,
  },
  helpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: Layout.spacing.md,
  },
  helpText: {
    fontSize: 14,
    marginRight: Layout.spacing.xs,
  },
  helpLink: {
    fontSize: 14,
    fontWeight: '600',
  },
});
