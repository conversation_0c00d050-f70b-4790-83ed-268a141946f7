/**
 * Product types for the CashyMall mobile app
 */

// Tag interface
export interface Tag {
  id: number;
  name: string;
  type: string;
}

// Category interface
export interface Category {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  type: string | null;
  parentId: number | null;
  parentName: string | null;
  subcategories: Category[] | null;
  productCount: number;
  createdAt: string;
  updatedAt: string;
  active: boolean;
}

// Category list response interface
export interface CategoryListResponse {
  data: Category[];
  message: string | null;
  success: boolean;
  errorType: string | null;
}

// Subcategory interface
export interface Subcategory {
  id: number;
  name: string;
  imageUrl: string;
}

// Product interface
export interface Product {
  id: number;
  name: string;
  description: string;
  quantity: number;
  price: number;
  producer: string;
  imageUrl: string;
  material: string;
  care: string;
  additionalImages: string[];
  sizes: string | null;
  fit: string | null;
  features: string;
  colors: string;
  origin: string;
  sku: string;
  discountPrice: number ;
  stockLevel: number;
  barcode: string;
  weight: number;
  dimensions: string;
  categoryId: number;
  categoryName: string;
  subcategories: Subcategory[] | null;
  brandId: number;
  brandName: string;
  averageRating: number | null;
  reviewCount: number;
  tags: Tag[];
  createdAt: string;
  updatedAt: string;
  active: boolean;
  featured: boolean;
}

// Product list response interface
export interface ProductListResponse {
  data: Product[];
  message: string | null;
  success: boolean;
  errorType: string | null;
}

// Product detail response interface
export interface ProductDetailResponse {
  data: Product;
  message: string | null;
  success: boolean;
  errorType: string | null;
}

// Product search params interface
export interface ProductSearchParams {
  query?: string;
  categoryId?: number;
  brandId?: number;
  minPrice?: number;
  maxPrice?: number;
  featured?: boolean;
  sortBy?: "price" | "name" | "newest" | "rating";
  sortOrder?: "asc" | "desc";
  page?: number;
  limit?: number;
}

// Brand interface
export interface Brand {
  id: number;
  name: string;
  logo: string;
  productCount: number;
  featured: boolean;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;

  brandCategoryId: number;
  brandCategoryName: string;
  description: string;

  websiteUrl: string;
  links: string[];
  enabled: true;
}

// Order status type
export type OrderStatus =
  | "PENDING"
  | "PROCESSING"
  | "SHIPPED"
  | "DELIVERED"
  | "CANCELLED"
  | "REFUNDED";

// Payment status type
export type PaymentStatus = "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED";

// Order item interface
export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  productSku: string;
  productPrice: number;
  quantity: number;
  subtotal: number;
  size: string;
  color: string;
  brandId: number;
  brandName: string;
  status: OrderStatus;
  rejectionReason: string | null;
  acceptedDate: string | null;
  rejectedDate: string | null;
  processedDate: string | null;
  shippedDate: string | null;
  deliveredDate: string | null;
  cancelledDate: string | null;
  returnedDate: string | null;
  refundedDate: string | null;
  productSnapshot: string; // JSON string containing product details
}

// Order interface
export interface Order {
  id: number;
  orderNumber: string;
  customerId: number;
  customerName: string;
  customerEmail: string;
  status: OrderStatus;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shippingCost: number;
  discount: number;
  total: number;
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZipCode: string;
  shippingCountry: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  billingCountry: string;
  paymentMethod: string | null;
  paymentTransactionId: string;
  paymentStatus: PaymentStatus;
  notes: string;
  orderDate: string;
  processedDate: string | null;
  shippedDate: string | null;
  deliveredDate: string | null;
  cancelledDate: string | null;
  refundedDate: string | null;
}

export interface CartItem {
  id: number;
  productId: number;
  productName: string;
  productSku: string;
  productPrice: number;
  discountPrice: number;
  quantity: number;
  subtotal: number;
  size: string;
  color: string;
  brandId: number;
  brandName: string;
  imageUrl: string;
  addedAt: string;
  updatedAt: string;
}

export interface CartResponse {
  id: number;
  userId: number;
  items: CartItem[];
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
  itemCount: number;
  message?: string | null;
  success?: boolean;
  errorType?: string | null;
}

export interface Wishlist {
  id: string;
  userId: number;
 
  name: string;
  productSku: string;

  brandName: string;
  categoryId: number;
  categoryName: string;
  description: string;
  price: number;
  discountPrice: number;
  imageUrl: string;
  addedAt: string;
  updatedAt: string;
}

export interface WishlistResponse {
  id: number;
  userId: number;
  items: Wishlist[];
  itemCount: number;
  message?: string | null;
  success?: boolean;
  errorType?: string | null;
}
