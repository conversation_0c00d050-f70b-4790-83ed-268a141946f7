import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useContext } from 'react';
import {
    Alert,
    Image,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import Button from '../../components/Button';
import Header from '../../components/Header';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { AuthContext } from '../../context/AuthContext';

export default function ProfileScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();
  const { user, isSignedIn, signOut } = useContext(AuthContext)!;

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Error', 'Failed to logout. Please try again.');
    }
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  const menuItems = [
    {
      id: 'orders',
      title: 'My Orders',
      icon: 'receipt-outline' as const,
      onPress: () => router.push('/profile/orders'),
    },
    {
      id: 'wishlist',
      title: 'Wishlist',
      icon: 'heart-outline' as const,
      onPress: () => router.push('/profile/wishlist'),
    },
    {
      id: 'addresses',
      title: 'Shipping Addresses',
      icon: 'location-outline' as const,
      onPress: () => router.push('/profile/addresses'),
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings-outline' as const,
      onPress: () => router.push('/profile/settings'),
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline' as const,
      onPress: () => router.push('/profile/support'),
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="Profile" showBack={false} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {isSignedIn && user ? (
          <View style={[styles.profileHeader, { backgroundColor: colors.cardBackground }]}>
            <Image
              source={{ uri: user.profilePicture || 'https://randomuser.me/api/portraits/men/32.jpg' }}
              style={styles.avatar}
            />
            <View style={styles.profileInfo}>
              <Text style={[styles.profileName, { color: colors.text }]}>
                {`${user.firstname} ${user.lastname }`}
              </Text>
              <Text style={[styles.profileEmail, { color: colors.tabIconDefault }]}>
                {user.email}
              </Text>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => router.push('/profile/edit')}
              >
                <Text style={[styles.editButtonText, { color: colors.primary }]}>
                  Edit Profile
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={[styles.loginPrompt, { backgroundColor: colors.cardBackground }]}>
            <Ionicons name="person-circle-outline" size={64} color={colors.tabIconDefault} />
            <Text style={[styles.loginPromptTitle, { color: colors.text }]}>
              Sign in to your account
            </Text>
            <Text style={[styles.loginPromptSubtitle, { color: colors.tabIconDefault }]}>
              Sign in to access your orders, wishlist, and personalized recommendations
            </Text>
            <Button
              title="Sign In"
              style={styles.loginButton}
              onPress={handleLogin}
            />
          </View>
        )}

        <View style={styles.menuContainer}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={[styles.menuItem, { backgroundColor: colors.cardBackground }]}
              onPress={item.onPress}
            >
              <View style={styles.menuItemLeft}>
                <Ionicons name={item.icon} size={22} color={colors.primary} style={styles.menuItemIcon} />
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>
                  {item.title}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.tabIconDefault} />
            </TouchableOpacity>
          ))}
        </View>

        {isSignedIn && (
          <Button
            title="Logout"
            variant="outline"
            style={styles.logoutButton}
            onPress={handleLogout}
          />
        )}

        <View style={styles.appInfo}>
          <Text style={[styles.appVersion, { color: colors.tabIconDefault }]}>
            Version 1.0.0
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    flexDirection: 'row',
    padding: Layout.spacing.md,
    marginHorizontal: Layout.spacing.md,
    marginTop: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileInfo: {
    marginLeft: Layout.spacing.md,
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.xs,
  },
  profileEmail: {
    fontSize: 14,
    marginBottom: Layout.spacing.sm,
  },
  editButton: {
    alignSelf: 'flex-start',
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loginPrompt: {
    padding: Layout.spacing.lg,
    marginHorizontal: Layout.spacing.md,
    marginTop: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  loginPromptTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xs,
  },
  loginPromptSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.md,
  },
  loginButton: {
    width: 200,
  },
  menuContainer: {
    marginTop: Layout.spacing.lg,
    marginHorizontal: Layout.spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.sm,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
      web: {
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
      }
    }),
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemIcon: {
    marginRight: Layout.spacing.sm,
  },
  menuItemTitle: {
    fontSize: 16,
  },
  logoutButton: {
    marginHorizontal: Layout.spacing.md,
    marginTop: Layout.spacing.lg,
  },
  appInfo: {
    alignItems: 'center',
    marginTop: Layout.spacing.xl,
    marginBottom: Layout.spacing.lg,
  },
  appVersion: {
    fontSize: 12,
  },
});
