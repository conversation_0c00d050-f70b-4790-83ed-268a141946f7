/**
 * Order API service for the CashyMall mobile app
 */
import { post, get } from './index';
import { ORDER_ENDPOINTS, PAYMENT_ENDPOINTS } from './endpoints';

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  discountPrice?: number;
  quantity: number;
  imageUrl: string;
}

export interface ShippingAddress {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface OrderRequestItem {
  productId: number;
  quantity: number;
  color: string;
  size: string;

}

export interface OrderRequest {
  customerId: number;
  items: OrderRequestItem[];
  tax: number;
  shippingCost: number;
  discount: number;
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZipCode: string;
  shippingCountry: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  billingCountry: string;
  paymentTransactionId: string;
  notes: string;
}



/**
 * Place a new order
 * @param orderData Order request data
 * @returns Order response
 */
export async function placeOrder(orderData: OrderRequest) {
  const response = await post(ORDER_ENDPOINTS.CREATE, orderData);
  return response;
}

/**
 * Process payment for an order
 * @param orderId Order ID
 * @param paymentDetails Payment details
 * @returns Payment response
 */
export async function processPayment(orderId: string, paymentDetails: any) {
  const response = await post(`${PAYMENT_ENDPOINTS.PROCESS}/${orderId}`, paymentDetails);
  return response;
}

/**
 * Get available payment methods
 * @returns List of payment methods
 */
export async function getPaymentMethods() {
  const response = await get(PAYMENT_ENDPOINTS.METHODS);
  return response;
}
